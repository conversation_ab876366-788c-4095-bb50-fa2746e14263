<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周菜单模块完整测试 (修复版)</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 14px;
        }
        button:hover { 
            background: #0056b3; 
        }
        button.success { background: #28a745; }
        button.warning { background: #ffc107; color: #212529; }
        #console { 
            background: #f8f9fa; 
            border: 1px solid #ddd; 
            padding: 15px; 
            height: 400px; 
            overflow-y: auto; 
            font-family: 'Courier New', monospace; 
            font-size: 12px;
            white-space: pre-wrap;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        h1, h2 { color: #333; }
        .status { font-weight: bold; }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 周菜单模块完整测试 (修复版)</h1>
        <p>此测试页面已修复网络请求问题，支持完整的模块功能测试。</p>
        
        <div class="test-section">
            <div class="test-grid">
                <button onclick="runAllTests()" class="success">🚀 运行所有测试</button>
                <button onclick="testModulesOnly()">📦 仅测试模块加载</button>
                <button onclick="testDataOnly()">💾 仅测试数据功能</button>
                <button onclick="testAppInit()">🎯 测试应用初始化</button>
                <button onclick="clearConsole()" class="warning">🗑️ 清空控制台</button>
            </div>
        </div>
        
        <div id="summary" class="summary" style="display: none;">
            <h3>📊 测试摘要</h3>
            <div id="summaryContent"></div>
        </div>
        
        <div id="results"></div>
        
        <div class="test-section">
            <h3>📋 详细日志：</h3>
            <div id="console"></div>
        </div>
    </div>

    <!-- 引入模块 -->
    <script src="utils/common.js"></script>
    <script src="core/MenuDataManager.js"></script>
    <script src="core/UIManager.js"></script>
    <script src="components/VirtualScroll.js"></script>
    <script src="components/RecipeSelector.js"></script>
    <script src="core/WeeklyMenuCore.js"></script>
    <script src="weekly_menu_main.js"></script>

    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        // 控制台重定向
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToDiv(type, ...args) {
            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    try {
                        return JSON.stringify(arg, null, 2);
                    } catch (e) {
                        return String(arg);
                    }
                }
                return String(arg);
            }).join(' ');
            
            const time = new Date().toLocaleTimeString();
            const colors = {
                log: '#28a745',
                error: '#dc3545', 
                warn: '#ffc107'
            };
            consoleDiv.innerHTML += `<div style="color: ${colors[type] || '#333'}; margin: 2px 0;">[${time}] ${type.toUpperCase()}: ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        console.log = (...args) => { originalLog(...args); logToDiv('log', ...args); };
        console.error = (...args) => { originalError(...args); logToDiv('error', ...args); };
        console.warn = (...args) => { originalWarn(...args); logToDiv('warn', ...args); };

        function clearConsole() {
            consoleDiv.innerHTML = '';
            testStats = { total: 0, passed: 0, failed: 0, warnings: 0 };
            document.getElementById('results').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
        }

        function addResult(success, message, details = '', isWarning = false) {
            testStats.total++;
            if (isWarning) {
                testStats.warnings++;
            } else if (success) {
                testStats.passed++;
            } else {
                testStats.failed++;
            }

            const div = document.createElement('div');
            const className = isWarning ? 'warning' : (success ? 'success' : 'error');
            div.className = `result ${className}`;
            const icon = isWarning ? '⚠️' : (success ? '✅' : '❌');
            div.innerHTML = `${icon} <span class="status">${message}</span>${details ? '<br><small>' + details + '</small>' : ''}`;
            document.getElementById('results').appendChild(div);
            
            updateSummary();
        }

        function addInfo(message) {
            const div = document.createElement('div');
            div.className = 'result info';
            div.innerHTML = `ℹ️ ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const contentDiv = document.getElementById('summaryContent');
            
            if (testStats.total > 0) {
                summaryDiv.style.display = 'block';
                const passRate = ((testStats.passed / testStats.total) * 100).toFixed(1);
                contentDiv.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center;">
                        <div><strong>总计</strong><br><span style="font-size: 1.5em;">${testStats.total}</span></div>
                        <div style="color: #28a745;"><strong>通过</strong><br><span style="font-size: 1.5em;">${testStats.passed}</span></div>
                        <div style="color: #dc3545;"><strong>失败</strong><br><span style="font-size: 1.5em;">${testStats.failed}</span></div>
                        <div style="color: #ffc107;"><strong>警告</strong><br><span style="font-size: 1.5em;">${testStats.warnings}</span></div>
                    </div>
                    <div style="margin-top: 10px; text-align: center;">
                        <strong>通过率: ${passRate}%</strong>
                    </div>
                `;
            }
        }

        function testModulesOnly() {
            clearConsole();
            console.log('🔍 仅测试模块加载...');
            testModuleLoading();
        }

        function testDataOnly() {
            clearConsole();
            console.log('💾 仅测试数据功能...');
            testModuleLoading();
            testCommonUtils();
            testMenuDataManager();
        }

        function testAppInit() {
            clearConsole();
            console.log('🎯 测试应用初始化...');
            testModuleLoading();
            testApplicationInit();
        }

        function runAllTests() {
            clearConsole();
            console.log('='.repeat(60));
            console.log('🚀 开始运行周菜单模块完整测试 (修复版)');
            console.log('='.repeat(60));

            // 环境检查
            console.log('🔍 环境检查:');
            console.log(`  浏览器: ${navigator.userAgent.split(' ').pop()}`);
            console.log(`  jQuery: ${typeof $ !== 'undefined' ? '已加载' : '未加载'}`);
            console.log(`  Fetch API: ${typeof fetch !== 'undefined' ? '支持' : '不支持'}`);
            console.log(`  测试模式: 已启用 (跳过网络请求)`);

            // 运行所有测试
            testModuleLoading();
            testCommonUtils();
            testMenuDataManager();
            testUIManager();
            testVirtualScroll();
            testApplicationInit();

            console.log('\n' + '='.repeat(60));
            console.log('✨ 所有测试完成');
            console.log('='.repeat(60));

            addInfo('🎉 测试完成！请查看上方摘要和详细结果。');
        }

        function testModuleLoading() {
            console.log('\n📦 测试1: 模块加载检查');
            const modules = [
                'CommonUtils', 
                'MenuDataManager', 
                'UIManager', 
                'VirtualScroll', 
                'RecipeSelector', 
                'WeeklyMenuCore', 
                'WeeklyMenuApp'
            ];
            
            let loadedCount = 0;
            const moduleResults = [];
            
            modules.forEach(moduleName => {
                const exists = window[moduleName] !== undefined;
                const hasInit = exists && typeof window[moduleName].init === 'function';
                console.log(`  ${moduleName}: ${exists ? '✅ 已加载' : '❌ 未加载'}${hasInit ? ' (有init方法)' : ''}`);
                moduleResults.push({ name: moduleName, loaded: exists, hasInit });
                if (exists) loadedCount++;
            });

            addResult(
                loadedCount === modules.length, 
                `模块加载测试 (${loadedCount}/${modules.length})`,
                `已加载: ${moduleResults.filter(m => m.loaded).map(m => m.name).join(', ')}`
            );
        }

        function testCommonUtils() {
            console.log('\n🛠️ 测试2: CommonUtils功能测试');
            if (window.CommonUtils) {
                try {
                    const tests = [
                        {
                            name: '菜品名称清理',
                            test: () => {
                                const result = CommonUtils.cleanRecipeName('红烧肉（测试学校）');
                                return result === '红烧肉';
                            }
                        },
                        {
                            name: '日期格式化',
                            test: () => {
                                const result = CommonUtils.formatDate(new Date('2024-01-01'), 'YYYY-MM-DD');
                                return result === '2024-01-01';
                            }
                        },
                        {
                            name: '深拷贝',
                            test: () => {
                                const obj = { a: { b: 1 } };
                                const cloned = CommonUtils.deepClone(obj);
                                return cloned.a.b === 1 && cloned !== obj;
                            }
                        },
                        {
                            name: '空值检查',
                            test: () => {
                                return CommonUtils.isEmpty('') && CommonUtils.isEmpty([]) && !CommonUtils.isEmpty('test');
                            }
                        },
                        {
                            name: '菜品验证',
                            test: () => {
                                const valid = { id: 1, name: '测试菜品' };
                                const invalid = { name: '测试菜品' }; // 缺少id
                                return CommonUtils.validateRecipe(valid) && !CommonUtils.validateRecipe(invalid);
                            }
                        }
                    ];

                    let passedTests = 0;
                    tests.forEach(test => {
                        try {
                            const result = test.test();
                            console.log(`  ${test.name}: ${result ? '✅' : '❌'}`);
                            if (result) passedTests++;
                        } catch (error) {
                            console.log(`  ${test.name}: ❌ (异常: ${error.message})`);
                        }
                    });

                    addResult(
                        passedTests === tests.length,
                        `CommonUtils功能测试 (${passedTests}/${tests.length})`,
                        `通过的测试: ${tests.slice(0, passedTests).map(t => t.name).join(', ')}`
                    );
                } catch (error) {
                    console.error('  CommonUtils测试异常:', error);
                    addResult(false, 'CommonUtils功能测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'CommonUtils功能测试', '模块未加载');
            }
        }

        function testMenuDataManager() {
            console.log('\n💾 测试3: MenuDataManager基础功能 (测试模式)');
            if (window.MenuDataManager) {
                try {
                    // 创建测试用的DOM元素
                    const menuIdInput = document.createElement('input');
                    menuIdInput.id = 'menu-id';
                    menuIdInput.value = 'test-menu-123';
                    document.body.appendChild(menuIdInput);

                    const areaIdInput = document.createElement('input');
                    areaIdInput.id = 'area-id';
                    areaIdInput.value = 'test-area-456';
                    document.body.appendChild(areaIdInput);

                    // 初始化测试数据 (启用测试模式)
                    const testData = {
                        '2024-01-01': {
                            '早餐': [
                                { id: 1, name: '测试菜品1', recipe_id: 1, recipe_name: '测试菜品1' }
                            ]
                        }
                    };
                    
                    MenuDataManager.init(testData, { testMode: true });
                    console.log('  数据管理器初始化: ✅ (测试模式，跳过网络请求)');

                    // 测试数据设置和获取
                    const newRecipes = [
                        { id: 2, name: '测试菜品2', recipe_id: 2, recipe_name: '测试菜品2' },
                        { id: 3, name: '测试菜品3', recipe_id: 3, recipe_name: '测试菜品3' }
                    ];
                    
                    const setResult = MenuDataManager.setRecipes('2024-01-02', '午餐', newRecipes);
                    const getResult = MenuDataManager.getRecipes('2024-01-02', '午餐');
                    
                    console.log(`  数据设置: ${setResult ? '✅' : '❌'}`);
                    console.log(`  数据获取: ${getResult.length === 2 ? '✅' : '❌'} (获取到${getResult.length}个菜品)`);
                    
                    // 测试完整数据获取
                    const fullData = MenuDataManager.getFullData();
                    const hasFullData = Object.keys(fullData).length > 0;
                    console.log(`  完整数据获取: ${hasFullData ? '✅' : '❌'}`);

                    // 测试变更状态
                    const hasChanges = MenuDataManager.hasUnsavedChanges();
                    console.log(`  变更状态检查: ${hasChanges ? '✅' : '❌'} (有未保存更改)`);

                    const allTestsPassed = setResult && getResult.length === 2 && hasFullData && hasChanges;
                    addResult(allTestsPassed, 'MenuDataManager功能测试 (测试模式)', 
                        `设置/获取/完整数据/变更状态: ${setResult}/${getResult.length === 2}/${hasFullData}/${hasChanges}`);

                    // 清理测试元素
                    document.body.removeChild(menuIdInput);
                    document.body.removeChild(areaIdInput);
                } catch (error) {
                    console.error('  MenuDataManager测试异常:', error);
                    addResult(false, 'MenuDataManager功能测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'MenuDataManager功能测试', '模块未加载');
            }
        }

        function testUIManager() {
            console.log('\n🎨 测试4: UIManager基础功能');
            if (window.UIManager) {
                try {
                    UIManager.init();
                    console.log('  UI管理器初始化: ✅');
                    
                    // 测试消息显示（不会实际显示Toast，只测试不报错）
                    UIManager.showMessage('测试消息', 'info');
                    console.log('  消息显示测试: ✅');

                    // 测试输入框更新
                    const testInput = document.createElement('div');
                    testInput.className = 'test-input';
                    const testRecipes = [{ name: '测试菜品A' }, { name: '测试菜品B' }];
                    UIManager.updateInputDisplay(testInput, testRecipes);
                    const displayCorrect = testInput.textContent.includes('测试菜品A');
                    console.log(`  输入框更新测试: ${displayCorrect ? '✅' : '❌'}`);

                    // 测试加载状态
                    UIManager.showLoading();
                    UIManager.hideLoading();
                    console.log('  加载状态测试: ✅');
                    
                    addResult(displayCorrect, 'UIManager功能测试', '初始化、消息显示、输入框更新、加载状态');
                } catch (error) {
                    console.error('  UIManager测试异常:', error);
                    addResult(false, 'UIManager功能测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'UIManager功能测试', '模块未加载');
            }
        }

        function testVirtualScroll() {
            console.log('\n📜 测试5: VirtualScroll组件');
            if (window.VirtualScroll) {
                try {
                    const container = document.createElement('div');
                    container.style.height = '200px';
                    const testItems = Array.from({length: 100}, (_, i) => ({ id: i, name: `项目${i}` }));
                    
                    const virtualScroll = VirtualScroll.create({
                        container: container,
                        items: testItems,
                        itemHeight: 30,
                        itemRenderer: (item) => `<div>${item.name}</div>`
                    });

                    virtualScroll.init();
                    console.log('  虚拟滚动初始化: ✅');
                    
                    virtualScroll.updateItems(testItems.slice(0, 50));
                    console.log('  虚拟滚动更新: ✅');

                    virtualScroll.destroy();
                    console.log('  虚拟滚动销毁: ✅');

                    addResult(true, 'VirtualScroll组件测试', '初始化、更新、销毁功能正常');
                } catch (error) {
                    console.error('  VirtualScroll测试异常:', error);
                    addResult(false, 'VirtualScroll组件测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'VirtualScroll组件测试', '模块未加载');
            }
        }

        function testApplicationInit() {
            console.log('\n🎯 测试6: 应用初始化 (测试模式)');
            if (window.WeeklyMenuApp) {
                try {
                    const testData = {
                        '2024-01-01': {
                            '早餐': [{ id: 1, name: '测试菜品', recipe_id: 1, recipe_name: '测试菜品' }]
                        }
                    };

                    WeeklyMenuApp.init(testData, { testMode: true }).then(() => {
                        const status = WeeklyMenuApp.getStatus();
                        console.log('  应用状态:', status);
                        console.log(`  应用初始化: ${status.initialized ? '✅' : '❌'}`);
                        console.log(`  模块数量: ${status.modules.length}`);
                        
                        addResult(status.initialized, '应用初始化测试 (测试模式)', 
                            `版本: ${status.version}, 模块: ${status.modules.length}个`);
                    }).catch(error => {
                        console.error('  应用初始化失败:', error);
                        addResult(false, '应用初始化测试', `失败: ${error.message}`);
                    });
                } catch (error) {
                    console.error('  应用初始化异常:', error);
                    addResult(false, '应用初始化测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, '应用初始化测试', 'WeeklyMenuApp未加载');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('📄 页面加载完成');
            addInfo('🎉 页面已加载完成！所有模块已修复网络请求问题。点击"运行所有测试"开始完整测试。');
        });
    </script>
</body>
</html>
