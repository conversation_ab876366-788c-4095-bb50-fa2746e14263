/**
 * 菜单数据管理模块
 * 使用 Map 数据结构高效存储和管理菜单数据
 * 
 * @module MenuDataManager
 * @version 2.0
 */

const MenuDataManager = {
    // 菜单数据 - 使用Map结构提高查找效率
    menuData: new Map(),

    // 副表数据 - 用于存储临时菜品数据
    tempData: new Map(),

    // 缓存和状态
    _cache: {
        lastModified: null,
        hasChanges: false,
        weeklyMenuId: null,
        areaId: null
    },

    /**
     * 初始化菜单数据管理器
     * @param {Object} initialData - 初始菜单数据
     * @returns {Object} this - 支持链式调用
     */
    init(initialData) {
        this.menuData.clear();
        this.tempData.clear();
        this._cache.lastModified = new Date();
        this._cache.hasChanges = false;
        // 兼容jQuery和原生DOM
        const menuIdElement = document.getElementById('menu-id');
        const areaIdElement = document.getElementById('area-id');
        this._cache.weeklyMenuId = menuIdElement ? menuIdElement.value : null;
        this._cache.areaId = areaIdElement ? areaIdElement.value : null;

        if (initialData && typeof initialData === 'object') {
            try {
                // 将对象结构转换为嵌套Map结构
                Object.entries(initialData).forEach(([date, meals]) => {
                    const dateMap = new Map();
                    if (meals && typeof meals === 'object') {
                        Object.entries(meals).forEach(([meal, recipes]) => {
                            if (Array.isArray(recipes)) {
                                dateMap.set(meal, [...recipes]);
                            } else {
                                console.warn(`初始化菜单数据: ${date} ${meal} 的菜品不是数组`);
                                dateMap.set(meal, []);
                            }
                        });
                    }
                    this.menuData.set(date, dateMap);
                });
                console.log('菜单数据初始化成功');

                // 初始化完成后，加载副表数据
                this.loadTempData();
            } catch (error) {
                console.error('菜单数据初始化失败:', error);
                // 初始化失败时使用空Map
                this.menuData.clear();
            }
        } else {
            // 如果没有初始数据，也尝试加载副表数据
            this.loadTempData();
        }

        console.log('菜单数据初始化完成');
        return this;
    },

    /**
     * 加载副表数据
     * @returns {boolean} 是否成功启动加载
     */
    loadTempData() {
        const weeklyMenuId = this._cache.weeklyMenuId;
        if (!weeklyMenuId) {
            console.warn('加载副表数据: 缺少菜单ID');
            return false;
        }

        console.log(`开始加载副表数据: 菜单ID=${weeklyMenuId}`);

        // 发送AJAX请求获取副表数据
        if (typeof $ !== 'undefined') {
            // 使用jQuery AJAX
            $.ajax({
                url: `/api/weekly-menu/${weeklyMenuId}/temp-recipes`,
                method: 'GET',
                dataType: 'json',
                success: (response) => {
                    if (response.success && response.data) {
                        this.processTempData(response.data);
                    } else {
                        console.warn('加载副表数据: 服务器返回空数据');
                    }
                },
                error: (xhr) => {
                    console.error('加载副表数据失败:', xhr.responseText);
                }
            });
        } else if (typeof fetch !== 'undefined') {
            // 使用原生fetch
            fetch(`/api/weekly-menu/${weeklyMenuId}/temp-recipes`)
                .then(response => response.json())
                .then(response => {
                    if (response.success && response.data) {
                        this.processTempData(response.data);
                    } else {
                        console.warn('加载副表数据: 服务器返回空数据');
                    }
                })
                .catch(error => {
                    console.error('加载副表数据失败:', error);
                });
        } else {
            console.warn('没有可用的AJAX方法 (jQuery或fetch)');
        }

        return true;
    },

    /**
     * 处理副表数据
     * @param {Array} data - 副表数据数组
     */
    processTempData(data) {
        try {
            if (!Array.isArray(data) || data.length === 0) {
                console.warn('处理副表数据: 数据为空');
                return;
            }

            console.log(`处理副表数据: ${data.length}条记录`);

            // 清空临时数据
            this.tempData.clear();

            // 处理每条记录
            data.forEach(item => {
                const date = item.day_of_week;
                const meal = item.meal_type;

                if (!date || !meal) {
                    console.warn('处理副表数据: 记录缺少日期或餐次信息', item);
                    return;
                }

                // 创建菜品对象
                const recipe = {
                    id: item.recipe_id || `temp_${Date.now()}`,
                    name: item.recipe_name || '未命名菜品',
                    recipe_id: item.recipe_id,
                    recipe_name: item.recipe_name,
                    is_custom: item.is_custom || false,
                    temp_id: item.id // 保存副表记录ID
                };

                // 添加到临时数据
                if (!this.tempData.has(date)) {
                    this.tempData.set(date, new Map());
                }

                if (!this.tempData.get(date).has(meal)) {
                    this.tempData.get(date).set(meal, []);
                }

                this.tempData.get(date).get(meal).push(recipe);
            });

            console.log('副表数据处理完成');

            // 更新主数据
            this.syncTempDataToMain();
        } catch (error) {
            console.error('处理副表数据失败:', error);
        }
    },

    /**
     * 将副表数据同步到主数据
     */
    syncTempDataToMain() {
        try {
            console.log('开始同步副表数据到主数据');

            this.tempData.forEach((mealMap, date) => {
                mealMap.forEach((recipes, meal) => {
                    if (recipes.length > 0) {
                        console.log(`同步数据: ${date} ${meal}, ${recipes.length}个菜品`);
                        this.setRecipes(date, meal, recipes);
                    }
                });
            });

            console.log('副表数据同步完成');

            // 更新UI
            this.updateAllInputs();
        } catch (error) {
            console.error('同步副表数据失败:', error);
        }
    },

    /**
     * 更新所有输入框显示
     */
    updateAllInputs() {
        this.menuData.forEach((mealMap, date) => {
            mealMap.forEach((recipes, meal) => {
                // 兼容jQuery和原生DOM查询
                let input;
                if (typeof $ !== 'undefined') {
                    input = $(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
                    if (input.length && window.UIManager) {
                        window.UIManager.updateInputDisplay(input, recipes);

                        // 将菜品ID存储到输入框的data属性中
                        const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
                        input.data('recipe-ids', recipeIds);
                    }
                } else {
                    // 使用原生DOM查询
                    input = document.querySelector(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
                    if (input && window.UIManager) {
                        window.UIManager.updateInputDisplay(input, recipes);

                        // 将菜品ID存储到输入框的data属性中
                        const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
                        input.dataset.recipeIds = JSON.stringify(recipeIds);
                    }
                }
            });
        });
    },

    /**
     * 获取指定日期和餐次的菜品列表
     * @param {string} date - 日期
     * @param {string} meal - 餐次
     * @returns {Array} 菜品列表的副本
     */
    getRecipes(date, meal) {
        const dateMap = this.menuData.get(date);
        if (!dateMap) return [];

        const recipes = dateMap.get(meal);
        return recipes ? [...recipes] : [];
    },

    /**
     * 设置指定日期和餐次的菜品列表
     * @param {string} date - 日期
     * @param {string} meal - 餐次
     * @param {Array} recipes - 菜品列表
     * @returns {boolean} 是否设置成功
     */
    setRecipes(date, meal, recipes) {
        if (!Array.isArray(recipes)) {
            console.error('设置菜品列表失败: recipes不是数组');
            return false;
        }

        try {
            // 确保日期Map存在
            if (!this.menuData.has(date)) {
                this.menuData.set(date, new Map());
            }

            // 确保每个菜品都有recipe_id
            const processedRecipes = recipes.map(recipe => {
                // 创建新对象，避免修改原始对象
                const newRecipe = {...recipe};

                // 如果recipe_id为空但id存在，则使用id作为recipe_id
                if (!newRecipe.recipe_id && newRecipe.id) {
                    newRecipe.recipe_id = newRecipe.id;
                    console.log(`修正菜品数据: 使用id(${newRecipe.id})作为recipe_id`);
                }

                // 如果是自定义菜品且没有recipe_id，设置为null
                if (newRecipe.is_custom && !newRecipe.recipe_id) {
                    newRecipe.recipe_id = null;
                    console.log(`自定义菜品: ${newRecipe.name}, recipe_id设置为null`);
                }

                // 确保recipe_name存在
                if (!newRecipe.recipe_name && newRecipe.name) {
                    newRecipe.recipe_name = newRecipe.name;
                }

                return newRecipe;
            });

            // 设置菜品列表
            this.menuData.get(date).set(meal, processedRecipes);

            // 标记数据已更改
            this._cache.hasChanges = true;
            this._cache.lastModified = new Date();

            console.log(`已设置菜品列表: ${date} ${meal}, ${processedRecipes.length}个菜品`);

            return true;
        } catch (error) {
            console.error('设置菜品列表失败:', error);
            return false;
        }
    },

    /**
     * 检查是否有未保存的更改
     * @returns {boolean} 是否有未保存的更改
     */
    hasUnsavedChanges() {
        return this._cache.hasChanges;
    },

    /**
     * 重置更改状态
     */
    resetChangeStatus() {
        this._cache.hasChanges = false;
    },

    /**
     * 获取完整数据（用于保存）
     * @returns {Object} 完整的菜单数据对象
     */
    getFullData() {
        const result = {};

        this.menuData.forEach((mealMap, date) => {
            result[date] = {};

            mealMap.forEach((recipes, meal) => {
                result[date][meal] = [...recipes];
            });
        });

        return result;
    }
};

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuDataManager;
} else if (typeof window !== 'undefined') {
    window.MenuDataManager = MenuDataManager;
}
