/**
 * 周菜单核心模块
 * 负责系统初始化、事件绑定和核心业务逻辑
 * 
 * @module WeeklyMenuCore
 * @version 2.0
 */

const WeeklyMenuCore = {
    // 状态
    state: {
        isSaving: false,
        hasUnsavedChanges: false,
        currentWeek: null
    },

    /**
     * 初始化周菜单核心模块
     * @param {Object} initialData - 初始菜单数据
     * @param {Object} options - 初始化选项
     */
    init(initialData, options = {}) {
        // 初始化菜单数据
        if (window.MenuDataManager) {
            window.MenuDataManager.init(initialData, options);
        }

        // 初始化UI管理器
        if (window.UIManager) {
            window.UIManager.init();
        }

        // 初始化菜品选择器
        if (window.RecipeSelector) {
            window.RecipeSelector.init();
        }

        // 绑定事件
        this.bindEvents();

        console.log('周菜单核心模块初始化完成');
    },

    /**
     * 绑定事件
     */
    bindEvents() {
        // 菜单输入框点击事件
        $('.menu-input').on('click', (e) => {
            if ($(e.target).hasClass('readonly')) return;

            const date = $(e.target).data('date');
            const meal = $(e.target).data('meal');
            
            if (window.RecipeSelector) {
                window.RecipeSelector.showModal(date, meal);
            }
        });

        // 保存菜单按钮点击事件
        $('#saveMenuBtn').on('click', () => {
            this.saveMenu();
        });

        // 发布菜单按钮点击事件
        $('#publishMenuBtn').on('click', () => {
            this.publishMenu();
        });

        // 解除发布按钮点击事件
        $('#unpublishMenuBtn').on('click', () => {
            this.unpublishMenu();
        });

        // 创建菜单按钮点击事件
        $('#createMenuBtn').on('click', () => {
            this.createMenu();
        });

        // 周次选择器点击事件
        $('.week-item').on('click', (e) => {
            const weekStart = $(e.target).data('week');
            // 检查是否是活动项
            if ($(e.target).hasClass('active')) return;

            // 如果有未保存的更改，提示用户
            if (this.hasUnsavedChanges()) {
                if (window.UIManager) {
                    window.UIManager.showConfirm('您有未保存的更改，确定要离开吗？', () => {
                        this.loadWeek(weekStart);
                    });
                }
            } else {
                this.loadWeek(weekStart);
            }
        });
    },

    /**
     * 检查是否有未保存的更改
     * @returns {boolean} 是否有未保存的更改
     */
    hasUnsavedChanges() {
        return window.MenuDataManager ? window.MenuDataManager.hasUnsavedChanges() : false;
    },

    /**
     * 加载指定周次的菜单
     * @param {string} weekStart - 周开始日期
     */
    loadWeek(weekStart) {
        const areaId = $('#area-id').val();
        window.location.href = `/weekly-menu-v2/plan?area_id=${areaId}&week_start=${weekStart}`;
    },

    /**
     * 创建菜单
     */
    createMenu() {
        if (window.UIManager) {
            window.UIManager.showLoading();
        }

        const areaId = $('#area-id').val();
        const weekStart = $('#week-start').val();
        const csrfToken = $('meta[name="csrf-token"]').attr('content');

        // 发送创建请求
        $.ajax({
            url: '/api/weekly-menu/create',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': csrfToken
            },
            data: JSON.stringify({
                area_id: areaId,
                week_start: weekStart
            }),
            success: (response) => {
                console.log('创建周菜单响应:', response);

                if (response.success) {
                    // 隐藏创建提示，显示菜单表单
                    $('#createMenuPrompt').hide();
                    $('#menuForm').show();

                    // 设置菜单ID
                    $('#menu-id').val(response.weekly_menu_id);

                    // 更新缓存中的菜单ID
                    if (window.MenuDataManager) {
                        window.MenuDataManager._cache.weeklyMenuId = response.weekly_menu_id;
                    }

                    // 显示成功消息
                    if (window.UIManager) {
                        window.UIManager.showMessage('周菜单创建成功，正在刷新页面...', 'success');
                    }

                    // 延迟刷新页面以确保状态完全更新
                    setTimeout(() => {
                        const areaId = $('#area-id').val();
                        const weekStart = $('#week-start').val();
                        window.location.href = `/weekly-menu-v2/plan?area_id=${areaId}&week_start=${weekStart}`;
                    }, 1500);
                } else {
                    console.error('创建周菜单失败:', response.message);
                    if (window.UIManager) {
                        window.UIManager.showMessage('创建失败: ' + (response.message || '未知错误'), 'error');
                    }
                }
            },
            error: (xhr) => {
                console.error('创建周菜单请求失败:', xhr.responseText);
                if (window.UIManager) {
                    window.UIManager.showError('创建周菜单请求失败: ' + xhr.responseText);
                }
            },
            complete: () => {
                if (window.UIManager) {
                    window.UIManager.hideLoading();
                }
            }
        });
    },

    /**
     * 保存菜单
     */
    saveMenu() {
        try {
            if (window.UIManager) {
                window.UIManager.showLoading();
            }

            // 获取菜单ID和区域ID
            const menuId = $('#menu-id').val();
            const areaId = $('#area-id').val();

            if (!menuId || !areaId) {
                if (window.UIManager) {
                    window.UIManager.showError('保存失败: 缺少菜单ID或区域ID');
                    window.UIManager.hideLoading();
                }
                return;
            }

            console.log(`准备保存菜单: ID=${menuId}, 区域ID=${areaId}`);

            // 在提交前，确保所有格子的数据都已更新到MenuDataManager
            this._ensureAllCellsDataSaved();

            // 获取完整的菜单数据
            const menuData = window.MenuDataManager ? window.MenuDataManager.getFullData() : {};
            console.log('菜单数据:', menuData);

            // 准备表单数据
            $('#menuData').val(JSON.stringify(menuData));

            // 提交表单（后端会同时保存到主表和副表）
            $('#menuForm').submit();
        } catch (error) {
            console.error('保存菜单失败:', error);
            if (window.UIManager) {
                window.UIManager.showError('保存菜单失败: ' + error.message);
                window.UIManager.hideLoading();
            }
        }
    },

    /**
     * 确保所有格子的数据都已保存到MenuDataManager
     * @private
     */
    _ensureAllCellsDataSaved() {
        try {
            console.log('确保所有格子数据已保存...');

            // 遍历所有菜单输入框
            $('.menu-input').each((index, input) => {
                const $input = $(input);
                const date = $input.data('date');
                const meal = $input.data('meal');

                if (!date || !meal) {
                    console.warn('输入框缺少日期或餐次信息:', input);
                    return;
                }

                // 检查输入框是否有菜品
                if ($input.hasClass('has-recipes') && window.MenuDataManager) {
                    // 获取当前MenuDataManager中的数据
                    const currentRecipes = window.MenuDataManager.getRecipes(date, meal);

                    // 如果MenuDataManager中没有数据，但输入框显示有菜品
                    if (currentRecipes.length === 0) {
                        console.log(`发现未同步数据: ${date} ${meal}`);
                        this._recoverDataFromInput($input, date, meal, index);
                    } else {
                        console.log(`格子数据已同步: ${date} ${meal}, ${currentRecipes.length}个菜品`);
                    }
                }
            });

            console.log('所有格子数据检查完成');
            return true;
        } catch (error) {
            console.error('确保所有格子数据已保存失败:', error);
            return false;
        }
    },

    /**
     * 从输入框恢复数据
     * @param {jQuery} $input - 输入框jQuery对象
     * @param {string} date - 日期
     * @param {string} meal - 餐次
     * @param {number} index - 索引
     * @private
     */
    _recoverDataFromInput($input, date, meal, index) {
        // 尝试从输入框的data属性获取菜品ID
        const recipeIds = $input.data('recipe-ids');

        if (recipeIds && recipeIds.length > 0) {
            console.log(`从输入框恢复菜品数据: ${date} ${meal}, IDs=${recipeIds}`);

            // 创建菜品对象
            const recipes = recipeIds.map(id => {
                // 如果是自定义菜品(以custom_开头)
                if (id && typeof id === 'string' && id.startsWith('custom_')) {
                    return {
                        id: id,
                        name: $input.text().trim(),
                        recipe_id: null,
                        recipe_name: $input.text().trim(),
                        is_custom: true
                    };
                }

                // 普通菜品
                return {
                    id: id,
                    name: $input.text().trim(),
                    recipe_id: id,
                    recipe_name: $input.text().trim()
                };
            });

            // 更新MenuDataManager
            if (window.MenuDataManager) {
                window.MenuDataManager.setRecipes(date, meal, recipes);
            }
            console.log(`已恢复菜品数据: ${date} ${meal}, ${recipes.length}个菜品`);
        } else {
            // 如果没有保存菜品ID，尝试从文本内容创建
            const text = $input.text().trim();
            if (text && !text.includes('未选择菜品')) {
                console.log(`从文本创建菜品: ${date} ${meal}, 文本="${text}"`);

                // 创建自定义菜品
                const customId = 'custom_' + Date.now() + '_' + index;
                const recipe = {
                    id: customId,
                    name: text,
                    recipe_id: null,
                    recipe_name: text,
                    is_custom: true
                };

                // 更新MenuDataManager
                if (window.MenuDataManager) {
                    window.MenuDataManager.setRecipes(date, meal, [recipe]);
                }
                console.log(`已创建自定义菜品: ${date} ${meal}, ID=${customId}`);
            }
        }
    },

    /**
     * 发布菜单
     */
    publishMenu() {
        const menuId = $('#menu-id').val();
        if (!menuId) {
            if (window.UIManager) {
                window.UIManager.showMessage('请先保存菜单', 'error');
            }
            return;
        }

        if (window.UIManager) {
            window.UIManager.showConfirm('确定要发布此菜单吗？发布后将无法修改。', () => {
                window.UIManager.showLoading();

                $.ajax({
                    url: `/api/weekly-menu/${menuId}/publish`,
                    method: 'POST',
                    success: (response) => {
                        if (response.success) {
                            window.UIManager.showMessage('菜单发布成功', 'success');
                            // 刷新页面
                            location.reload();
                        } else {
                            window.UIManager.showMessage(response.message, 'error');
                        }
                    },
                    error: (xhr) => {
                        window.UIManager.showMessage('发布菜单失败: ' + xhr.responseText, 'error');
                    },
                    complete: () => {
                        window.UIManager.hideLoading();
                    }
                });
            });
        }
    },

    /**
     * 解除发布
     */
    unpublishMenu() {
        const menuId = $('#menu-id').val();
        if (!menuId) {
            if (window.UIManager) {
                window.UIManager.showMessage('请先保存菜单', 'error');
            }
            return;
        }

        if (window.UIManager) {
            window.UIManager.showConfirm('确定要解除发布此菜单吗？', () => {
                window.UIManager.showLoading();

                $.ajax({
                    url: `/api/weekly-menu/${menuId}/unpublish`,
                    method: 'POST',
                    success: (response) => {
                        if (response.success) {
                            window.UIManager.showMessage('菜单已解除发布', 'success');
                            // 刷新页面
                            location.reload();
                        } else {
                            window.UIManager.showMessage(response.message, 'error');
                        }
                    },
                    error: (xhr) => {
                        window.UIManager.showMessage('解除发布失败: ' + xhr.responseText, 'error');
                    },
                    complete: () => {
                        window.UIManager.hideLoading();
                    }
                });
            });
        }
    }
};

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WeeklyMenuCore;
} else if (typeof window !== 'undefined') {
    window.WeeklyMenuCore = WeeklyMenuCore;
}
