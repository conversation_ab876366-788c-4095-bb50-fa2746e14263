# 周菜单管理系统 - 前端模块化架构

## 概述

本项目采用模块化设计，将原本的单一文件拆分为多个独立的模块，提高了代码的可维护性、可扩展性和可测试性。

## 目录结构

```
app/static/js/weekly_menu/
├── core/                          # 核心模块
│   ├── MenuDataManager.js         # 菜单数据管理
│   ├── UIManager.js              # UI交互管理
│   └── WeeklyMenuCore.js         # 核心业务逻辑
├── components/                    # 组件模块
│   ├── RecipeSelector.js         # 菜品选择器
│   └── VirtualScroll.js          # 虚拟滚动组件
├── utils/                        # 工具模块
│   └── common.js                 # 通用工具函数
├── weekly_menu_main.js           # 主入口文件
└── README.md                     # 说明文档
```

## 模块说明

### 核心模块 (core/)

#### MenuDataManager.js
- **功能**: 菜单数据的存储、管理和同步
- **特性**: 
  - 使用 Map 数据结构优化查找效率
  - 支持主表和副表数据同步
  - 提供数据变更追踪
- **主要方法**:
  - `init(initialData)`: 初始化数据
  - `getRecipes(date, meal)`: 获取菜品列表
  - `setRecipes(date, meal, recipes)`: 设置菜品列表
  - `hasUnsavedChanges()`: 检查未保存更改

#### UIManager.js
- **功能**: 用户界面交互和消息提示
- **特性**:
  - 统一的消息提示系统
  - 加载状态管理
  - Bootstrap 4/5 兼容性
- **主要方法**:
  - `showMessage(message, type)`: 显示消息
  - `showLoading()`: 显示加载状态
  - `updateInputDisplay(input, recipes)`: 更新输入框显示

#### WeeklyMenuCore.js
- **功能**: 核心业务逻辑和事件协调
- **特性**:
  - 统一的事件绑定
  - 菜单保存、发布、创建等操作
  - 模块间协调
- **主要方法**:
  - `init(initialData)`: 初始化核心模块
  - `saveMenu()`: 保存菜单
  - `publishMenu()`: 发布菜单

### 组件模块 (components/)

#### RecipeSelector.js
- **功能**: 菜品选择模态框管理
- **特性**:
  - 菜品搜索和分类筛选
  - 自定义菜品添加
  - 菜品名称智能处理（去除学校信息）
- **主要方法**:
  - `showModal(date, meal)`: 显示选择模态框
  - `addToSelection(recipe)`: 添加菜品到选择
  - `saveSelection()`: 保存选择结果

#### VirtualScroll.js
- **功能**: 虚拟滚动组件（性能优化）
- **特性**:
  - 大数据量渲染优化
  - 可配置的缓冲区大小
  - 自动计算可见区域
- **使用场景**: 当菜品数量很大时提升渲染性能

### 工具模块 (utils/)

#### common.js
- **功能**: 通用工具函数和辅助方法
- **包含功能**:
  - 防抖和节流函数
  - 深拷贝和日期格式化
  - 菜品数据验证和标准化
  - 本地存储工具
  - 浏览器兼容性检查

### 主入口文件

#### weekly_menu_main.js
- **功能**: 应用初始化和模块管理
- **特性**:
  - 自动模块加载和初始化
  - 全局错误处理
  - 兼容性检查
  - 生命周期管理

## 使用方法

### 在HTML模板中引入

```html
<!-- 按顺序引入模块 -->
<script src="{{ url_for('static', filename='js/weekly_menu/utils/common.js') }}"></script>
<script src="{{ url_for('static', filename='js/weekly_menu/core/MenuDataManager.js') }}"></script>
<script src="{{ url_for('static', filename='js/weekly_menu/core/UIManager.js') }}"></script>
<script src="{{ url_for('static', filename='js/weekly_menu/components/VirtualScroll.js') }}"></script>
<script src="{{ url_for('static', filename='js/weekly_menu/components/RecipeSelector.js') }}"></script>
<script src="{{ url_for('static', filename='js/weekly_menu/core/WeeklyMenuCore.js') }}"></script>
<script src="{{ url_for('static', filename='js/weekly_menu/weekly_menu_main.js') }}"></script>
```

### 手动初始化

```javascript
// 如果需要手动初始化
const initialData = { /* 菜单数据 */ };
WeeklyMenuApp.init(initialData);
```

### 访问模块

```javascript
// 获取特定模块
const dataManager = WeeklyMenuApp.getModule('MenuDataManager');
const uiManager = WeeklyMenuApp.getModule('UIManager');

// 检查模块是否可用
if (WeeklyMenuApp.hasModule('RecipeSelector')) {
    // 使用模块
}
```

## 优势

### 1. 模块化设计
- **职责分离**: 每个模块专注于特定功能
- **低耦合**: 模块间通过明确的接口通信
- **高内聚**: 相关功能集中在同一模块

### 2. 可维护性
- **代码组织**: 清晰的目录结构和命名规范
- **文档完善**: 详细的注释和类型说明
- **错误处理**: 统一的错误处理机制

### 3. 可扩展性
- **插件化**: 新功能可以作为独立模块添加
- **配置化**: 支持灵活的配置选项
- **版本管理**: 模块独立版本控制

### 4. 性能优化
- **按需加载**: 可以根据需要加载特定模块
- **虚拟滚动**: 大数据量渲染优化
- **缓存机制**: 智能的数据缓存策略

### 5. 兼容性
- **浏览器兼容**: 支持现代浏览器和部分旧版本
- **框架兼容**: Bootstrap 4/5 兼容
- **降级处理**: 优雅的功能降级

## 迁移说明

### 从旧版本迁移
1. **备份原文件**: 保留 `weekly_menu_v2.js` 作为备份
2. **更新模板**: 修改HTML模板中的脚本引用
3. **测试功能**: 确保所有功能正常工作
4. **清理代码**: 删除不再需要的旧文件

### 注意事项
- 确保按正确顺序加载模块
- 检查自定义代码是否需要适配
- 验证所有功能的兼容性

## 开发指南

### 添加新模块
1. 在相应目录下创建模块文件
2. 遵循现有的模块结构和命名规范
3. 在 `weekly_menu_main.js` 中注册新模块
4. 更新文档和测试

### 调试技巧
- 使用浏览器开发者工具
- 检查控制台日志输出
- 利用模块的状态检查方法
- 使用 `WeeklyMenuApp.getStatus()` 查看应用状态

## 版本历史

- **v2.0**: 模块化重构版本
  - 拆分为独立模块
  - 改进错误处理
  - 增强兼容性
  - 性能优化

- **v1.0**: 原始单文件版本
  - 基础功能实现
  - 单一文件结构
