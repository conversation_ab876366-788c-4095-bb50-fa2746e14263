/**
 * UI交互管理模块
 * 负责用户界面的交互、消息提示、加载状态等
 * 
 * @module UIManager
 * @version 2.0
 */

const UIManager = {
    // 配置选项
    config: {
        messageTimeout: 3000,
        errorTimeout: 10000,
        ajaxStatusTimeout: 5000,
        animationDuration: 300,
        toastPosition: 'top-right'
    },

    // 状态管理
    state: {
        isLoading: false,
        activeModals: new Set(),
        notifications: []
    },

    /**
     * 初始化UI管理器
     * @returns {Object} this - 支持链式调用
     */
    init() {
        // 创建Toast容器
        this._createToastContainer();

        // 使用事件委托绑定全局事件
        this._bindGlobalEvents();

        // 初始化加载指示器
        this._initLoadingIndicator();

        console.log('UI管理器初始化完成');
        return this;
    },

    /**
     * 创建Toast容器
     * @private
     */
    _createToastContainer() {
        if (document.querySelector('.toast-container')) return;
        
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed ' + this.config.toastPosition;
        container.style.zIndex = '1090';
        document.body.appendChild(container);
        this._toastContainer = container;
    },

    /**
     * 绑定全局事件
     * @private
     */
    _bindGlobalEvents() {
        // 使用事件委托处理模态框事件
        $(document).on('show.bs.modal', '.modal', (e) => {
            const modalId = e.target.id;
            this.state.activeModals.add(modalId);
        });

        $(document).on('hidden.bs.modal', '.modal', (e) => {
            const modalId = e.target.id;
            this.state.activeModals.delete(modalId);
        });

        // 监听菜单数据变更事件
        document.addEventListener('menudata:changed', (e) => {
            if (e.detail.hasChanges) {
                this._updateSaveIndicator(true);
            } else {
                this._updateSaveIndicator(false);
            }
        });

        // 监听网络状态
        window.addEventListener('online', () => {
            this.showMessage('网络连接已恢复', 'success');
        });

        window.addEventListener('offline', () => {
            this.showMessage('网络连接已断开', 'warning');
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时，检查是否需要刷新数据
                this._checkDataFreshness();
            }
        });
    },

    /**
     * 初始化加载指示器
     * @private
     */
    _initLoadingIndicator() {
        // 检查是否已存在加载指示器
        if (!document.querySelector('.loading-overlay')) {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;
            overlay.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            `;
            document.body.appendChild(overlay);
        }
    },

    /**
     * 更新保存指示器
     * @param {boolean} hasChanges - 是否有未保存的更改
     * @private
     */
    _updateSaveIndicator(hasChanges) {
        const saveBtn = document.getElementById('saveMenuBtn');
        if (saveBtn) {
            if (hasChanges) {
                saveBtn.classList.add('btn-warning');
                saveBtn.classList.remove('btn-primary');
                saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存菜单 <span class="badge bg-light">*</span>';
            } else {
                saveBtn.classList.remove('btn-warning');
                saveBtn.classList.add('btn-primary');
                saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存菜单';
            }
        }
    },

    /**
     * 检查数据新鲜度
     * @private
     */
    _checkDataFreshness() {
        // 如果页面隐藏超过一定时间，可能需要刷新数据
        const lastActive = this._lastActiveTime || 0;
        const now = Date.now();
        const threshold = 5 * 60 * 1000; // 5分钟

        if (now - lastActive > threshold) {
            // 提示用户刷新
            this.showConfirm('页面数据可能已过期，是否刷新？', () => {
                location.reload();
            });
        }

        this._lastActiveTime = now;
    },

    /**
     * 显示加载状态
     * @returns {Object} this - 支持链式调用
     */
    showLoading() {
        this.state.isLoading = true;
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
        return this;
    },

    /**
     * 隐藏加载状态
     * @returns {Object} this - 支持链式调用
     */
    hideLoading() {
        this.state.isLoading = false;
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
        return this;
    },

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (info, success, warning, error)
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Object} this - 支持链式调用
     */
    showMessage(message, type = 'info', timeout = null) {
        if (!message) return this;

        // 确定超时时间
        const duration = timeout || (type === 'error' ? this.config.errorTimeout : this.config.messageTimeout);

        // 创建Toast元素
        const toastId = 'toast-' + Date.now();
        const toast = document.createElement('div');
        toast.className = `toast bg-${this._getBootstrapColor(type)}`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        toast.setAttribute('data-delay', duration);
        toast.setAttribute('id', toastId);

        toast.innerHTML = `
            <div class="toast-header">
                <strong class="mr-auto">${this._getMessageTitle(type)}</strong>
                <small>${this._formatTime(new Date())}</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body text-white">
                ${message}
            </div>
        `;

        // 添加到容器
        if (this._toastContainer) {
            this._toastContainer.appendChild(toast);

            // 显示Toast
            if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                const bsToast = new bootstrap.Toast(toast, { delay: duration });
                bsToast.show();
            } else {
                // 降级处理
                toast.style.display = 'block';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, duration);
            }
        }

        return this;
    },

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     * @returns {Object} this - 支持链式调用
     */
    showError(message) {
        return this.showMessage(message, 'error');
    },

    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     * @returns {Object} this - 支持链式调用
     */
    showSuccess(message) {
        return this.showMessage(message, 'success');
    },

    /**
     * 显示确认对话框
     * @param {string} message - 确认消息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    showConfirm(message, onConfirm, onCancel) {
        if (confirm(message)) {
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        } else {
            if (typeof onCancel === 'function') {
                onCancel();
            }
        }
    },

    /**
     * 更新输入框显示
     * @param {jQuery} input - 输入框jQuery对象
     * @param {Array} recipes - 菜品列表
     */
    updateInputDisplay(input, recipes) {
        if (!input || !input.length) return;

        if (recipes && recipes.length > 0) {
            const recipeNames = recipes.map(r => r.name || r.recipe_name || '未命名菜品');
            input.text(recipeNames.join(', '));
            input.addClass('has-recipes');
            input.removeClass('text-muted');
        } else {
            input.text('未选择菜品');
            input.removeClass('has-recipes');
            input.addClass('text-muted');
        }
    },

    /**
     * 获取Bootstrap颜色类
     * @param {string} type - 消息类型
     * @returns {string} Bootstrap颜色类
     * @private
     */
    _getBootstrapColor(type) {
        const colorMap = {
            info: 'info',
            success: 'success',
            warning: 'warning',
            error: 'danger'
        };
        return colorMap[type] || 'info';
    },

    /**
     * 获取消息标题
     * @param {string} type - 消息类型
     * @returns {string} 消息标题
     * @private
     */
    _getMessageTitle(type) {
        const titleMap = {
            info: '提示',
            success: '成功',
            warning: '警告',
            error: '错误'
        };
        return titleMap[type] || '提示';
    },

    /**
     * 格式化时间
     * @param {Date} date - 日期对象
     * @returns {string} 格式化的时间字符串
     * @private
     */
    _formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
};

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
} else if (typeof window !== 'undefined') {
    window.UIManager = UIManager;
}
