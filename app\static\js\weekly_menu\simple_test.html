<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周菜单模块简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #console { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>周菜单模块简单测试</h1>
    
    <button onclick="runAllTests()">运行所有测试</button>
    <button onclick="clearConsole()">清空控制台</button>
    
    <div id="results"></div>
    <h3>控制台输出：</h3>
    <div id="console"></div>

    <!-- jQuery (如果需要) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 引入模块 -->
    <script src="utils/common.js"></script>
    <script src="core/MenuDataManager.js"></script>
    <script src="core/UIManager.js"></script>
    <script src="components/VirtualScroll.js"></script>
    <script src="components/RecipeSelector.js"></script>
    <script src="core/WeeklyMenuCore.js"></script>
    <script src="weekly_menu_main.js"></script>

    <script>
        // 控制台重定向
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToDiv(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            const time = new Date().toLocaleTimeString();
            consoleDiv.innerHTML += `<div style="color: ${type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black'}">[${time}] ${type.toUpperCase()}: ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        console.log = (...args) => { originalLog(...args); logToDiv('log', ...args); };
        console.error = (...args) => { originalError(...args); logToDiv('error', ...args); };
        console.warn = (...args) => { originalWarn(...args); logToDiv('warn', ...args); };

        function clearConsole() {
            consoleDiv.innerHTML = '';
        }

        function addResult(success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.textContent = `${success ? '✓' : '✗'} ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function runAllTests() {
            document.getElementById('results').innerHTML = '';
            console.log('=== 开始运行所有测试 ===');

            // 测试1: 检查模块是否加载
            console.log('测试1: 检查模块加载');
            const modules = ['CommonUtils', 'MenuDataManager', 'UIManager', 'VirtualScroll', 'RecipeSelector', 'WeeklyMenuCore', 'WeeklyMenuApp'];
            let allModulesLoaded = true;
            
            modules.forEach(moduleName => {
                const exists = window[moduleName] !== undefined;
                console.log(`  ${moduleName}: ${exists ? '已加载' : '未加载'}`);
                if (!exists) allModulesLoaded = false;
            });
            addResult(allModulesLoaded, `模块加载测试 - ${allModulesLoaded ? '全部成功' : '部分失败'}`);

            // 测试2: 检查CommonUtils功能
            if (window.CommonUtils) {
                console.log('测试2: CommonUtils功能测试');
                try {
                    // 测试菜品名称清理
                    const testName = '红烧肉（测试学校）';
                    const cleaned = CommonUtils.cleanRecipeName(testName);
                    const nameTestPass = cleaned === '红烧肉';
                    console.log(`  菜品名称清理: "${testName}" -> "${cleaned}" ${nameTestPass ? '✓' : '✗'}`);

                    // 测试日期格式化
                    const testDate = new Date('2024-01-01');
                    const formatted = CommonUtils.formatDate(testDate, 'YYYY-MM-DD');
                    const dateTestPass = formatted === '2024-01-01';
                    console.log(`  日期格式化: ${formatted} ${dateTestPass ? '✓' : '✗'}`);

                    // 测试深拷贝
                    const obj = { a: { b: 1 } };
                    const cloned = CommonUtils.deepClone(obj);
                    const cloneTestPass = cloned.a.b === 1 && cloned !== obj;
                    console.log(`  深拷贝测试: ${cloneTestPass ? '✓' : '✗'}`);

                    const utilsTestPass = nameTestPass && dateTestPass && cloneTestPass;
                    addResult(utilsTestPass, 'CommonUtils功能测试');
                } catch (error) {
                    console.error('  CommonUtils测试失败:', error);
                    addResult(false, 'CommonUtils功能测试 - 异常');
                }
            } else {
                addResult(false, 'CommonUtils功能测试 - 模块未加载');
            }

            // 测试3: MenuDataManager基础功能
            if (window.MenuDataManager) {
                console.log('测试3: MenuDataManager功能测试');
                try {
                    // 初始化测试数据
                    const testData = {
                        '2024-01-01': {
                            '早餐': [
                                { id: 1, name: '测试菜品1', recipe_id: 1, recipe_name: '测试菜品1' }
                            ]
                        }
                    };
                    
                    MenuDataManager.init(testData);
                    console.log('  数据管理器初始化完成');

                    // 测试数据设置和获取
                    const newRecipes = [
                        { id: 2, name: '测试菜品2', recipe_id: 2, recipe_name: '测试菜品2' },
                        { id: 3, name: '测试菜品3', recipe_id: 3, recipe_name: '测试菜品3' }
                    ];
                    
                    const setResult = MenuDataManager.setRecipes('2024-01-02', '午餐', newRecipes);
                    const getResult = MenuDataManager.getRecipes('2024-01-02', '午餐');
                    
                    const dataTestPass = setResult && getResult.length === 2 && getResult[0].name === '测试菜品2';
                    console.log(`  数据设置/获取测试: ${dataTestPass ? '✓' : '✗'}`);
                    
                    addResult(dataTestPass, 'MenuDataManager功能测试');
                } catch (error) {
                    console.error('  MenuDataManager测试失败:', error);
                    addResult(false, 'MenuDataManager功能测试 - 异常');
                }
            } else {
                addResult(false, 'MenuDataManager功能测试 - 模块未加载');
            }

            // 测试4: UIManager基础功能
            if (window.UIManager) {
                console.log('测试4: UIManager功能测试');
                try {
                    UIManager.init();
                    console.log('  UI管理器初始化完成');
                    
                    // 测试消息显示（不会实际显示，只测试不报错）
                    UIManager.showMessage('测试消息', 'info');
                    console.log('  消息显示测试: ✓');
                    
                    addResult(true, 'UIManager功能测试');
                } catch (error) {
                    console.error('  UIManager测试失败:', error);
                    addResult(false, 'UIManager功能测试 - 异常');
                }
            } else {
                addResult(false, 'UIManager功能测试 - 模块未加载');
            }

            // 测试5: 应用初始化
            if (window.WeeklyMenuApp) {
                console.log('测试5: 应用初始化测试');
                try {
                    const testData = {
                        '2024-01-01': {
                            '早餐': [{ id: 1, name: '测试菜品', recipe_id: 1, recipe_name: '测试菜品' }]
                        }
                    };

                    WeeklyMenuApp.init(testData).then(() => {
                        const status = WeeklyMenuApp.getStatus();
                        console.log('  应用状态:', status);
                        addResult(status.initialized, '应用初始化测试');
                    }).catch(error => {
                        console.error('  应用初始化失败:', error);
                        addResult(false, '应用初始化测试 - 失败');
                    });
                } catch (error) {
                    console.error('  应用初始化异常:', error);
                    addResult(false, '应用初始化测试 - 异常');
                }
            } else {
                addResult(false, '应用初始化测试 - WeeklyMenuApp未加载');
            }

            console.log('=== 测试完成 ===');
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('页面加载完成');
            setTimeout(() => {
                console.log('开始自动测试...');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
