/**
 * 周菜单管理系统 - 主入口文件
 * 负责模块加载、初始化和全局配置
 * 
 * @version 2.0
 * <AUTHOR>
 * @description 模块化重构版本，提供更好的可维护性和扩展性
 */

(function(window) {
    'use strict';

    // 全局配置
    const CONFIG = {
        version: '2.0',
        debug: false,
        modules: [
            'CommonUtils',
            'MenuDataManager', 
            'UIManager',
            'VirtualScroll',
            'RecipeSelector',
            'WeeklyMenuCore'
        ]
    };

    /**
     * 周菜单应用主类
     */
    class WeeklyMenuApp {
        constructor() {
            this.initialized = false;
            this.modules = new Map();
            this.config = CONFIG;
        }

        /**
         * 初始化应用
         * @param {Object} initialData - 初始菜单数据
         * @param {Object} options - 初始化选项
         * @returns {Promise} 初始化Promise
         */
        async init(initialData = {}, options = {}) {
            if (this.initialized) {
                console.warn('WeeklyMenuApp already initialized');
                return;
            }

            try {
                console.log('开始初始化周菜单应用...');

                // 检查浏览器兼容性
                this._checkCompatibility();

                // 等待DOM加载完成
                await this._waitForDOM();

                // 注册模块
                this._registerModules();

                // 初始化核心模块
                await this._initializeModules(initialData, options);

                // 设置全局错误处理
                this._setupGlobalErrorHandling();

                this.initialized = true;
                console.log('周菜单应用初始化完成');

                // 触发初始化完成事件
                this._dispatchEvent('app:initialized', { app: this });

            } catch (error) {
                console.error('周菜单应用初始化失败:', error);
                this._handleInitError(error);
                throw error;
            }
        }

        /**
         * 检查浏览器兼容性
         * @private
         */
        _checkCompatibility() {
            if (window.CommonUtils) {
                const compatibility = window.CommonUtils.checkCompatibility();
                if (!compatibility.isModern) {
                    console.warn('浏览器兼容性警告:', compatibility.features);
                    if (window.UIManager) {
                        window.UIManager.showMessage('您的浏览器版本较旧，可能影响部分功能', 'warning');
                    }
                }
            }
        }

        /**
         * 等待DOM加载完成
         * @returns {Promise} DOM加载Promise
         * @private
         */
        _waitForDOM() {
            return new Promise((resolve) => {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', resolve);
                } else {
                    resolve();
                }
            });
        }

        /**
         * 注册模块
         * @private
         */
        _registerModules() {
            this.config.modules.forEach(moduleName => {
                if (window[moduleName]) {
                    this.modules.set(moduleName, window[moduleName]);
                    console.log(`模块已注册: ${moduleName}`);
                } else {
                    console.warn(`模块未找到: ${moduleName}`);
                }
            });
        }

        /**
         * 初始化模块
         * @param {Object} initialData - 初始数据
         * @param {Object} options - 初始化选项
         * @returns {Promise} 初始化Promise
         * @private
         */
        async _initializeModules(initialData, options = {}) {
            // 按顺序初始化模块
            const initOrder = [
                'CommonUtils',
                'UIManager',
                'MenuDataManager',
                'RecipeSelector',
                'WeeklyMenuCore'
            ];

            for (const moduleName of initOrder) {
                const module = this.modules.get(moduleName);
                if (module && typeof module.init === 'function') {
                    try {
                        if (moduleName === 'MenuDataManager' || moduleName === 'WeeklyMenuCore') {
                            await module.init(initialData, options);
                        } else {
                            await module.init();
                        }
                        console.log(`模块初始化完成: ${moduleName}`);
                    } catch (error) {
                        console.error(`模块初始化失败: ${moduleName}`, error);
                    }
                }
            }
        }

        /**
         * 设置全局错误处理
         * @private
         */
        _setupGlobalErrorHandling() {
            // 捕获未处理的Promise错误
            window.addEventListener('unhandledrejection', (event) => {
                console.error('未处理的Promise错误:', event.reason);
                if (window.UIManager) {
                    window.UIManager.showError('系统错误: ' + event.reason.message);
                }
            });

            // 捕获全局JavaScript错误
            window.addEventListener('error', (event) => {
                console.error('全局JavaScript错误:', event.error);
                if (window.UIManager) {
                    window.UIManager.showError('系统错误: ' + event.error.message);
                }
            });
        }

        /**
         * 处理初始化错误
         * @param {Error} error - 错误对象
         * @private
         */
        _handleInitError(error) {
            // 显示用户友好的错误信息
            const errorMessage = '系统初始化失败，请刷新页面重试';
            
            // 尝试使用UIManager显示错误
            if (window.UIManager && typeof window.UIManager.showError === 'function') {
                window.UIManager.showError(errorMessage);
            } else {
                // 降级到alert
                alert(errorMessage);
            }
        }

        /**
         * 触发自定义事件
         * @param {string} eventName - 事件名称
         * @param {Object} detail - 事件详情
         * @private
         */
        _dispatchEvent(eventName, detail = {}) {
            const event = new CustomEvent(eventName, { detail });
            window.dispatchEvent(event);
        }

        /**
         * 获取模块
         * @param {string} moduleName - 模块名称
         * @returns {Object|null} 模块对象
         */
        getModule(moduleName) {
            return this.modules.get(moduleName) || null;
        }

        /**
         * 检查模块是否可用
         * @param {string} moduleName - 模块名称
         * @returns {boolean} 是否可用
         */
        hasModule(moduleName) {
            return this.modules.has(moduleName);
        }

        /**
         * 获取应用状态
         * @returns {Object} 应用状态
         */
        getStatus() {
            return {
                initialized: this.initialized,
                version: this.config.version,
                modules: Array.from(this.modules.keys()),
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 销毁应用
         */
        destroy() {
            // 清理模块
            this.modules.forEach((module, name) => {
                if (typeof module.destroy === 'function') {
                    try {
                        module.destroy();
                        console.log(`模块已销毁: ${name}`);
                    } catch (error) {
                        console.error(`模块销毁失败: ${name}`, error);
                    }
                }
            });

            this.modules.clear();
            this.initialized = false;

            console.log('周菜单应用已销毁');
        }
    }

    // 创建全局应用实例
    const app = new WeeklyMenuApp();

    // 暴露到全局
    window.WeeklyMenuApp = app;

    // 自动初始化（如果jQuery可用）
    if (typeof $ !== 'undefined') {
        $(document).ready(() => {
            // 获取初始菜单数据
            const menuDataElement = document.getElementById('menuData');
            let initialData = {};

            if (menuDataElement && menuDataElement.value) {
                try {
                    initialData = JSON.parse(menuDataElement.value);
                } catch (e) {
                    console.error('解析菜单数据失败:', e);
                }
            }

            // 初始化应用
            app.init(initialData).catch(error => {
                console.error('应用自动初始化失败:', error);
            });
        });
    } else {
        console.warn('jQuery未找到，请手动初始化应用');
    }

})(window);
