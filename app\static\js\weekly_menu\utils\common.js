/**
 * 通用工具函数
 * 提供常用的工具方法和辅助函数
 * 
 * @module CommonUtils
 * @version 2.0
 */

const CommonUtils = {
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @param {boolean} immediate - 是否立即执行
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深拷贝对象
     * @param {*} obj - 要拷贝的对象
     * @returns {*} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }

        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }

        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }

        return obj;
    },

    /**
     * 格式化日期
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} format - 格式字符串 (YYYY-MM-DD, YYYY/MM/DD等)
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        if (isNaN(d.getTime())) {
            return '';
        }

        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 生成唯一ID
     * @param {string} prefix - 前缀
     * @returns {string} 唯一ID
     */
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    /**
     * 检查是否为空值
     * @param {*} value - 要检查的值
     * @returns {boolean} 是否为空
     */
    isEmpty(value) {
        if (value === null || value === undefined) {
            return true;
        }
        if (typeof value === 'string') {
            return value.trim() === '';
        }
        if (Array.isArray(value)) {
            return value.length === 0;
        }
        if (typeof value === 'object') {
            return Object.keys(value).length === 0;
        }
        return false;
    },

    /**
     * 安全地获取对象属性
     * @param {Object} obj - 对象
     * @param {string} path - 属性路径 (如: 'a.b.c')
     * @param {*} defaultValue - 默认值
     * @returns {*} 属性值或默认值
     */
    safeGet(obj, path, defaultValue = null) {
        try {
            const keys = path.split('.');
            let result = obj;
            for (const key of keys) {
                if (result === null || result === undefined) {
                    return defaultValue;
                }
                result = result[key];
            }
            return result !== undefined ? result : defaultValue;
        } catch (error) {
            return defaultValue;
        }
    },

    /**
     * 清理菜品名称（去除学校信息）
     * @param {string} name - 原始菜品名称
     * @returns {string} 清理后的菜品名称
     */
    cleanRecipeName(name) {
        if (!name || typeof name !== 'string') {
            return '';
        }
        // 去除括号及其内容 - 支持中文和英文括号
        return name.replace(/[（(].*?[）)]/g, '').trim();
    },

    /**
     * 验证菜品数据
     * @param {Object} recipe - 菜品对象
     * @returns {boolean} 是否有效
     */
    validateRecipe(recipe) {
        if (!recipe || typeof recipe !== 'object') {
            return false;
        }
        
        // 必须有ID和名称
        if (!recipe.id || !recipe.name) {
            return false;
        }

        return true;
    },

    /**
     * 标准化菜品对象
     * @param {Object} recipe - 原始菜品对象
     * @returns {Object} 标准化后的菜品对象
     */
    normalizeRecipe(recipe) {
        if (!this.validateRecipe(recipe)) {
            return null;
        }

        const normalized = {
            id: recipe.id,
            name: this.cleanRecipeName(recipe.name),
            recipe_id: recipe.recipe_id || recipe.id,
            recipe_name: this.cleanRecipeName(recipe.recipe_name || recipe.name),
            is_custom: recipe.is_custom || false
        };

        // 如果是自定义菜品且没有recipe_id，设置为null
        if (normalized.is_custom && !normalized.recipe_id) {
            normalized.recipe_id = null;
        }

        return normalized;
    },

    /**
     * 检查浏览器兼容性
     * @returns {Object} 兼容性信息
     */
    checkCompatibility() {
        const features = {
            es6: typeof Symbol !== 'undefined',
            map: typeof Map !== 'undefined',
            set: typeof Set !== 'undefined',
            promise: typeof Promise !== 'undefined',
            fetch: typeof fetch !== 'undefined',
            localStorage: (() => {
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    return true;
                } catch (e) {
                    return false;
                }
            })()
        };

        return {
            isModern: Object.values(features).every(Boolean),
            features
        };
    },

    /**
     * 错误处理包装器
     * @param {Function} fn - 要包装的函数
     * @param {string} context - 上下文信息
     * @returns {Function} 包装后的函数
     */
    errorWrapper(fn, context = 'Unknown') {
        return function(...args) {
            try {
                return fn.apply(this, args);
            } catch (error) {
                console.error(`Error in ${context}:`, error);
                if (window.UIManager) {
                    window.UIManager.showError(`操作失败: ${error.message}`);
                }
                return null;
            }
        };
    },

    /**
     * 本地存储工具
     */
    storage: {
        /**
         * 设置本地存储
         * @param {string} key - 键
         * @param {*} value - 值
         * @returns {boolean} 是否成功
         */
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('LocalStorage set error:', error);
                return false;
            }
        },

        /**
         * 获取本地存储
         * @param {string} key - 键
         * @param {*} defaultValue - 默认值
         * @returns {*} 存储的值或默认值
         */
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('LocalStorage get error:', error);
                return defaultValue;
            }
        },

        /**
         * 删除本地存储
         * @param {string} key - 键
         * @returns {boolean} 是否成功
         */
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('LocalStorage remove error:', error);
                return false;
            }
        },

        /**
         * 清空本地存储
         * @returns {boolean} 是否成功
         */
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('LocalStorage clear error:', error);
                return false;
            }
        }
    }
};

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CommonUtils;
} else if (typeof window !== 'undefined') {
    window.CommonUtils = CommonUtils;
}
