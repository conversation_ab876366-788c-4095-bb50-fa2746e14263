# AI 编程规范与思考指南

## 1. 基本原则

### 1.1 最小修改原则
- 只修改必要的代码
- 不改变已经正常工作的功能
- 保持现有代码结构和风格
- 优先使用现有功能，而不是重写

### 1.2 兼容性原则
- 确保新代码与现有系统兼容
- 遵循项目的技术栈和架构设计
- 使用统一的编码风格和命名规范
- 保持与现有模块的一致性

### 1.3 可追溯性原则
- 记录所有修改的原因和目的
- 保持代码修改的可追踪性
- 提供清晰的修改说明
- 便于后续维护和调试

## 2. 上下文理解规范

### 2.1 项目理解阶段
- 理解项目的整体架构
- 分析模块之间的关系
- 了解数据流向
- 掌握核心功能实现

### 2.2 功能理解
- 理解每个模块的具体功能
- 了解功能之间的依赖关系
- 掌握业务逻辑
- 理解用户需求

### 2.3 技术栈理解
- 了解使用的框架和库
- 掌握项目的技术特点
- 理解代码风格和规范
- 了解性能要求

## 3. 代码修改规范

### 3.1 修改前准备
- 仔细阅读现有代码
- 理解代码的功能和目的
- 确认修改的必要性
- 评估修改的影响范围

### 3.2 修改执行
- 保持代码风格一致
- 遵循项目的命名规范
- 添加必要的注释
- 确保代码的可读性

### 3.3 修改后验证
- 验证修改的正确性
- 确保不影响其他功能
- 更新相关文档
- 记录修改日志

## 4. 功能添加规范

### 4.1 新功能开发
- 遵循模块化设计
- 使用现有的技术栈
- 保持代码结构清晰
- 确保功能独立性

### 4.2 功能集成
- 使用现有的接口
- 保持与现有系统的兼容
- 避免重复开发
- 确保功能可扩展

### 4.3 功能测试
- 编写完整的测试用例
- 确保功能正确性
- 验证与其他功能的兼容性
- 进行性能测试

## 5. 错误处理规范

### 5.1 错误预防
- 进行充分的代码审查
- 使用类型检查
- 添加必要的验证
- 预防潜在问题

### 5.2 错误处理
- 使用统一的错误处理机制
- 提供清晰的错误信息
- 记录详细的错误日志
- 确保系统稳定性

### 5.3 错误恢复
- 提供回滚机制
- 保持数据一致性
- 确保系统可恢复
- 提供故障排除指南

## 6. 文档规范

### 6.1 代码文档
- 添加必要的注释
- 说明代码功能
- 解释复杂逻辑
- 提供使用示例

### 6.2 项目文档
- 更新项目说明
- 记录功能变更
- 提供使用指南
- 维护技术文档

### 6.3 版本控制
- 使用语义化版本
- 记录版本变更
- 提供更新说明
- 维护版本历史

## 7. 性能规范

### 7.1 代码性能
- 优化算法效率
- 减少资源消耗
- 避免性能瓶颈
- 进行性能测试

### 7.2 系统性能
- 优化数据库查询
- 使用缓存机制
- 控制内存使用
- 确保响应速度

### 7.3 用户体验
- 优化界面响应
- 提供加载提示
- 处理异常情况
- 确保操作流畅

## 8. 安全规范

### 8.1 数据安全
- 保护敏感信息
- 使用加密存储
- 控制数据访问
- 确保数据完整性

### 8.2 系统安全
- 防止未授权访问
- 使用安全认证
- 防止SQL注入
- 防止XSS攻击

### 8.3 代码安全
- 使用安全的API
- 避免安全漏洞
- 进行安全测试
- 定期安全审查

## 9. 维护规范

### 9.1 代码维护
- 定期代码审查
- 及时修复问题
- 优化代码结构
- 更新依赖版本

### 9.2 系统维护
- 监控系统运行
- 定期备份数据
- 更新系统组件
- 优化系统性能

### 9.3 文档维护
- 更新技术文档
- 维护使用手册
- 记录问题解决方案
- 更新版本说明

## 10. 持续学习

### 10.1 知识积累
- 记录学习心得
- 总结技术要点
- 积累解决方案
- 建立知识库

### 10.2 技能提升
- 学习新技术
- 改进开发方法
- 提高代码质量
- 优化开发流程

### 10.3 经验分享
- 分享开发经验
- 交流技术问题
- 讨论最佳实践
- 促进团队成长

## 11. 每次对话的思考流程

### 11.1 对话前准备
- 仔细阅读和理解项目上下文
- 分析现有代码结构和功能
- 理解技术栈和架构设计
- 掌握业务逻辑和需求

### 11.2 修改前思考
- 评估修改的必要性
- 分析影响范围
- 确保与现有系统兼容
- 制定清晰的修改方案

### 11.3 执行修改原则
- 保持最小修改原则
- 不改变正常工作的功能
- 保持代码风格一致
- 确保修改可追踪

### 11.4 修改后验证
- 验证修改的正确性
- 确保不影响其他功能
- 更新相关文档
- 记录修改日志

### 11.5 持续改进
- 总结每次修改的经验
- 记录遇到的问题
- 提出改进建议
- 分享最佳实践
根据 README.md 中的解决方案，我来详细说明如何处理时间的问题：

1. **模型定义层面**：
```python
from sqlalchemy.dialects.mssql import DATETIME2
from datetime import datetime

class StandardModel(db.Model):
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 使用 DATETIME2 类型，precision=1 表示精确到 0.1 秒
    created_at = db.Column(DATETIME2(precision=1), 
                          default=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), 
                          default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)
```

2. **创建记录时**：
```python
from sqlalchemy import text

def create_record():
    try:
        # 注意：SQL 语句中不包含 created_at 和 updated_at 字段
        sql = text("""
            INSERT INTO table_name 
            (name, location, manager_id, status, notes)  
            OUTPUT inserted.id
            VALUES 
            (:name, :location, :manager_id, :status, :notes)
        """)
        
        params = {
            'name': form.name.data,
            'location': form.location.data,
            'manager_id': form.manager_id.data,
            'status': form.status.data,
            'notes': form.notes.data
        }
        
        result = db.session.execute(sql, params)
        record_id = result.fetchone()[0]
        db.session.commit()
        return record_id
        
    except Exception as e:
        db.session.rollback()
        raise
```

3. **更新记录时**：
```python
def update_record(record_id):
    try:
        # 注意：SQL 语句中不包含 updated_at 字段
        sql = text("""
            UPDATE table_name
            SET name = :name,
                location = :location,
                manager_id = :manager_id,
                status = :status,
                notes = :notes
            WHERE id = :id
        """)
        
        params = {
            'name': form.name.data,
            'location': form.location.data,
            'manager_id': form.manager_id.data,
            'status': form.status.data,
            'notes': form.notes.data,
            'id': record_id
        }
        
        db.session.execute(sql, params)
        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        raise
```

4. **关键点说明**：

- **不使用 ORM**：避免使用 SQLAlchemy ORM 的方式处理时间字段
- **不手动设置时间**：让数据库使用默认值处理 created_at 和 updated_at
- **精度控制**：使用 DATETIME2(precision=1) 控制时间精度到 0.1 秒
- **去除微秒**：使用 datetime.now().replace(microsecond=0) 去除微秒部分
- **参数化查询**：使用命名参数（:name）而不是问号占位符
- **异常处理**：使用 try-except 进行异常处理和回滚

5. **前端显示时间**：
```python
@app.template_filter('format_datetime')
def format_datetime_filter(value, format='%Y-%m-%d %H:%M'):
    """格式化日期时间，默认只精确到分钟"""
    if value is None:
        return ''
    if isinstance(value, datetime):
        return value.strftime(format)
    return value
```

6. **使用示例**：
```python
@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = ModelForm()
    if form.validate_on_submit():
        try:
            # 使用原始 SQL 创建记录
            sql = text("""
                INSERT INTO warehouses 
                (name, area_id, location, manager_id, status, notes)
                OUTPUT inserted.id
                VALUES 
                (:name, :area_id, :location, :manager_id, :status, :notes)
            """)
            
            result = db.session.execute(sql, {
                'name': form.name.data,
                'area_id': form.area_id.data,
                'location': form.location.data,
                'manager_id': form.manager_id.data,
                'status': form.status.data,
                'notes': form.notes.data
            })
            
            record_id = result.fetchone()[0]
            db.session.commit()
            
            flash('创建成功', 'success')
            return redirect(url_for('view', id=record_id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(str(e))
            flash('操作失败', 'danger')
            
    return render_template('form.html', form=form)
```

这种方案的优点是：
1. 避免了 SQLAlchemy 的时间戳处理问题
2. 保持了数据库时间戳的一致性
3. 减少了代码复杂度
4. 提高了代码的可维护性
5. 保证了数据的准确性

需要注意的是，这个方案是针对 MSSQL Server 的 DATETIME2 类型特别优化的，如果使用其他数据库，可能需要调整相应的实现细节。
