<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周菜单模块基础测试 (无jQuery)</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 14px;
        }
        button:hover { 
            background: #0056b3; 
        }
        #console { 
            background: #f8f9fa; 
            border: 1px solid #ddd; 
            padding: 15px; 
            height: 300px; 
            overflow-y: auto; 
            font-family: 'Courier New', monospace; 
            font-size: 12px;
            white-space: pre-wrap;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        h1, h2 { color: #333; }
        .status { font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 周菜单模块基础测试</h1>
        <p>此测试页面不依赖jQuery，测试模块的基础功能。</p>
        
        <div class="test-section">
            <button onclick="runAllTests()">🚀 运行所有测试</button>
            <button onclick="clearConsole()">🗑️ 清空控制台</button>
            <button onclick="testIndividual()">🔍 单独测试</button>
        </div>
        
        <div id="results"></div>
        
        <div class="test-section">
            <h3>📋 控制台输出：</h3>
            <div id="console"></div>
        </div>
    </div>

    <!-- 引入模块 (不包含jQuery) -->
    <script src="utils/common.js"></script>
    <script src="core/MenuDataManager.js"></script>
    <script src="core/UIManager.js"></script>
    <script src="components/VirtualScroll.js"></script>
    <script src="components/RecipeSelector.js"></script>
    <script src="core/WeeklyMenuCore.js"></script>
    <script src="weekly_menu_main.js"></script>

    <script>
        // 控制台重定向
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToDiv(type, ...args) {
            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    try {
                        return JSON.stringify(arg, null, 2);
                    } catch (e) {
                        return String(arg);
                    }
                }
                return String(arg);
            }).join(' ');
            
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#dc3545' : type === 'warn' ? '#ffc107' : '#28a745';
            consoleDiv.innerHTML += `<div style="color: ${color}; margin: 2px 0;">[${time}] ${type.toUpperCase()}: ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        console.log = (...args) => { originalLog(...args); logToDiv('log', ...args); };
        console.error = (...args) => { originalError(...args); logToDiv('error', ...args); };
        console.warn = (...args) => { originalWarn(...args); logToDiv('warn', ...args); };

        function clearConsole() {
            consoleDiv.innerHTML = '';
        }

        function addResult(success, message, details = '') {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            const icon = success ? '✅' : '❌';
            div.innerHTML = `${icon} <span class="status">${message}</span>${details ? '<br><small>' + details + '</small>' : ''}`;
            document.getElementById('results').appendChild(div);
        }

        function addInfo(message) {
            const div = document.createElement('div');
            div.className = 'result info';
            div.innerHTML = `ℹ️ ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function runAllTests() {
            document.getElementById('results').innerHTML = '';
            console.log('='.repeat(50));
            console.log('开始运行周菜单模块基础测试');
            console.log('='.repeat(50));

            // 环境检查
            console.log('🔍 环境检查:');
            console.log(`  浏览器: ${navigator.userAgent}`);
            console.log(`  jQuery: ${typeof $ !== 'undefined' ? '已加载' : '未加载'}`);
            console.log(`  Fetch API: ${typeof fetch !== 'undefined' ? '支持' : '不支持'}`);

            // 测试1: 模块加载检查
            console.log('\n📦 测试1: 模块加载检查');
            const modules = [
                'CommonUtils', 
                'MenuDataManager', 
                'UIManager', 
                'VirtualScroll', 
                'RecipeSelector', 
                'WeeklyMenuCore', 
                'WeeklyMenuApp'
            ];
            
            let loadedCount = 0;
            const moduleResults = [];
            
            modules.forEach(moduleName => {
                const exists = window[moduleName] !== undefined;
                const hasInit = exists && typeof window[moduleName].init === 'function';
                console.log(`  ${moduleName}: ${exists ? '✅ 已加载' : '❌ 未加载'}${hasInit ? ' (有init方法)' : ''}`);
                moduleResults.push({ name: moduleName, loaded: exists, hasInit });
                if (exists) loadedCount++;
            });

            addResult(
                loadedCount === modules.length, 
                `模块加载测试 (${loadedCount}/${modules.length})`,
                `已加载: ${moduleResults.filter(m => m.loaded).map(m => m.name).join(', ')}`
            );

            // 测试2: CommonUtils功能测试
            console.log('\n🛠️ 测试2: CommonUtils功能测试');
            if (window.CommonUtils) {
                try {
                    const tests = [
                        {
                            name: '菜品名称清理',
                            test: () => {
                                const result = CommonUtils.cleanRecipeName('红烧肉（测试学校）');
                                return result === '红烧肉';
                            }
                        },
                        {
                            name: '日期格式化',
                            test: () => {
                                const result = CommonUtils.formatDate(new Date('2024-01-01'), 'YYYY-MM-DD');
                                return result === '2024-01-01';
                            }
                        },
                        {
                            name: '深拷贝',
                            test: () => {
                                const obj = { a: { b: 1 } };
                                const cloned = CommonUtils.deepClone(obj);
                                return cloned.a.b === 1 && cloned !== obj;
                            }
                        },
                        {
                            name: '空值检查',
                            test: () => {
                                return CommonUtils.isEmpty('') && CommonUtils.isEmpty([]) && !CommonUtils.isEmpty('test');
                            }
                        },
                        {
                            name: '菜品验证',
                            test: () => {
                                const valid = { id: 1, name: '测试菜品' };
                                const invalid = { name: '测试菜品' }; // 缺少id
                                return CommonUtils.validateRecipe(valid) && !CommonUtils.validateRecipe(invalid);
                            }
                        }
                    ];

                    let passedTests = 0;
                    tests.forEach(test => {
                        try {
                            const result = test.test();
                            console.log(`  ${test.name}: ${result ? '✅' : '❌'}`);
                            if (result) passedTests++;
                        } catch (error) {
                            console.log(`  ${test.name}: ❌ (异常: ${error.message})`);
                        }
                    });

                    addResult(
                        passedTests === tests.length,
                        `CommonUtils功能测试 (${passedTests}/${tests.length})`,
                        `通过的测试: ${tests.slice(0, passedTests).map(t => t.name).join(', ')}`
                    );
                } catch (error) {
                    console.error('  CommonUtils测试异常:', error);
                    addResult(false, 'CommonUtils功能测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'CommonUtils功能测试', '模块未加载');
            }

            // 测试3: MenuDataManager基础功能
            console.log('\n💾 测试3: MenuDataManager基础功能');
            if (window.MenuDataManager) {
                try {
                    // 创建测试用的DOM元素
                    const menuIdInput = document.createElement('input');
                    menuIdInput.id = 'menu-id';
                    menuIdInput.value = 'test-menu-123';
                    document.body.appendChild(menuIdInput);

                    const areaIdInput = document.createElement('input');
                    areaIdInput.id = 'area-id';
                    areaIdInput.value = 'test-area-456';
                    document.body.appendChild(areaIdInput);

                    // 初始化测试数据
                    const testData = {
                        '2024-01-01': {
                            '早餐': [
                                { id: 1, name: '测试菜品1', recipe_id: 1, recipe_name: '测试菜品1' }
                            ]
                        }
                    };
                    
                    MenuDataManager.init(testData);
                    console.log('  数据管理器初始化: ✅');

                    // 测试数据设置和获取
                    const newRecipes = [
                        { id: 2, name: '测试菜品2', recipe_id: 2, recipe_name: '测试菜品2' },
                        { id: 3, name: '测试菜品3', recipe_id: 3, recipe_name: '测试菜品3' }
                    ];
                    
                    const setResult = MenuDataManager.setRecipes('2024-01-02', '午餐', newRecipes);
                    const getResult = MenuDataManager.getRecipes('2024-01-02', '午餐');
                    
                    console.log(`  数据设置: ${setResult ? '✅' : '❌'}`);
                    console.log(`  数据获取: ${getResult.length === 2 ? '✅' : '❌'} (获取到${getResult.length}个菜品)`);
                    
                    // 测试完整数据获取
                    const fullData = MenuDataManager.getFullData();
                    const hasFullData = Object.keys(fullData).length > 0;
                    console.log(`  完整数据获取: ${hasFullData ? '✅' : '❌'}`);

                    const allTestsPassed = setResult && getResult.length === 2 && hasFullData;
                    addResult(allTestsPassed, 'MenuDataManager功能测试', 
                        `设置/获取/完整数据: ${setResult}/${getResult.length === 2}/${hasFullData}`);

                    // 清理测试元素
                    document.body.removeChild(menuIdInput);
                    document.body.removeChild(areaIdInput);
                } catch (error) {
                    console.error('  MenuDataManager测试异常:', error);
                    addResult(false, 'MenuDataManager功能测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'MenuDataManager功能测试', '模块未加载');
            }

            // 测试4: UIManager基础功能
            console.log('\n🎨 测试4: UIManager基础功能');
            if (window.UIManager) {
                try {
                    UIManager.init();
                    console.log('  UI管理器初始化: ✅');
                    
                    // 测试消息显示（不会实际显示Toast，只测试不报错）
                    UIManager.showMessage('测试消息', 'info');
                    console.log('  消息显示测试: ✅');

                    // 测试输入框更新
                    const testInput = document.createElement('div');
                    testInput.className = 'test-input';
                    const testRecipes = [{ name: '测试菜品A' }, { name: '测试菜品B' }];
                    UIManager.updateInputDisplay(testInput, testRecipes);
                    const displayCorrect = testInput.textContent.includes('测试菜品A');
                    console.log(`  输入框更新测试: ${displayCorrect ? '✅' : '❌'}`);
                    
                    addResult(displayCorrect, 'UIManager功能测试', '初始化、消息显示、输入框更新');
                } catch (error) {
                    console.error('  UIManager测试异常:', error);
                    addResult(false, 'UIManager功能测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'UIManager功能测试', '模块未加载');
            }

            // 测试5: VirtualScroll组件
            console.log('\n📜 测试5: VirtualScroll组件');
            if (window.VirtualScroll) {
                try {
                    const container = document.createElement('div');
                    container.style.height = '200px';
                    const testItems = Array.from({length: 100}, (_, i) => ({ id: i, name: `项目${i}` }));
                    
                    const virtualScroll = VirtualScroll.create({
                        container: container,
                        items: testItems,
                        itemHeight: 30,
                        itemRenderer: (item) => `<div>${item.name}</div>`
                    });

                    virtualScroll.init();
                    console.log('  虚拟滚动初始化: ✅');
                    
                    virtualScroll.updateItems(testItems.slice(0, 50));
                    console.log('  虚拟滚动更新: ✅');

                    addResult(true, 'VirtualScroll组件测试', '初始化和更新功能正常');
                } catch (error) {
                    console.error('  VirtualScroll测试异常:', error);
                    addResult(false, 'VirtualScroll组件测试', `异常: ${error.message}`);
                }
            } else {
                addResult(false, 'VirtualScroll组件测试', '模块未加载');
            }

            console.log('\n' + '='.repeat(50));
            console.log('所有测试完成');
            console.log('='.repeat(50));

            addInfo('测试完成！请查看上方结果和控制台输出了解详细信息。');
        }

        function testIndividual() {
            addInfo('单独测试功能开发中...');
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('📄 页面加载完成');
            addInfo('页面已加载，点击"运行所有测试"开始测试模块功能。');
        });
    </script>
</body>
</html>
