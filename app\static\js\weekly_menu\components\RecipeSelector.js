/**
 * 菜品选择器模块
 * 管理菜品选择模态框的显示和交互
 * 
 * @module RecipeSelector
 * @version 2.0
 */

const RecipeSelector = {
    // 状态管理
    state: {
        currentDate: null,
        currentMeal: null,
        isSearching: false,
        lastSearchKeyword: '',
        lastCategory: 'all'
    },

    // 当前选择的菜品
    selectedRecipes: new Map(),

    /**
     * 初始化菜品选择器
     * @returns {Object} this - 支持链式调用
     */
    init() {
        console.log('初始化菜品选择器');
        this.bindEvents();
        return this;
    },

    /**
     * 使用事件委托绑定事件
     */
    bindEvents() {
        try {
            // 使用事件委托处理菜品分类切换
            $(document).on('click', '#recipeCategories .nav-link', (e) => {
                e.preventDefault();
                const category = $(e.currentTarget).data('category') || 'all';
                this.state.lastCategory = category;
                this.filterByCategory(category);
            });

            // 使用防抖处理菜品搜索
            let searchTimer;
            $(document).on('input', '#recipeSearch', (e) => {
                const keyword = $(e.target).val().trim();

                // 清除之前的定时器
                if (searchTimer) {
                    clearTimeout(searchTimer);
                }

                // 设置搜索状态
                this.state.isSearching = true;

                // 300ms防抖
                searchTimer = setTimeout(() => {
                    this.state.lastSearchKeyword = keyword;
                    this.searchRecipes(keyword);
                    this.state.isSearching = false;
                }, 300);
            });

            // 添加自定义菜品
            $(document).on('click', '#addCustomDishBtn', () => {
                this.addCustomRecipe();
            });

            // 回车添加自定义菜品
            $(document).on('keypress', '#customDishInput', (e) => {
                if (e.which === 13) {
                    e.preventDefault();
                    this.addCustomRecipe();
                }
            });

            // 使用事件委托处理菜品卡片点击
            $(document).on('click', '.recipe-card .card', (e) => {
                console.log('菜品卡片被点击', e.currentTarget);

                const card = $(e.currentTarget);
                const recipeId = card.data('id');
                const recipeNameWithSchool = card.data('name'); // 获取包含学校名称的完整菜品名

                // 去除学校名称 (括号及其内容) - 支持中文和英文括号
                const recipeName = recipeNameWithSchool ? 
                    recipeNameWithSchool.replace(/[（(].*?[）)]/g, '').trim() : '';

                console.log('菜品数据:', { 
                    id: recipeId, 
                    originalName: recipeNameWithSchool,
                    processedName: recipeName 
                });

                if (!recipeId || !recipeNameWithSchool) {
                    console.warn('菜品卡片缺少必要属性:', card);
                    return;
                }

                // 创建菜品对象
                const recipe = {
                    id: recipeId,
                    name: recipeName, // 使用处理后的纯菜品名
                    recipe_id: recipeId,
                    recipe_name: recipeName // 使用处理后的纯菜品名
                };

                // 添加到已选菜品
                this.addToSelection(recipe);
            });

            // 保存选择
            $(document).on('click', '#saveSelectionBtn', () => {
                this.saveSelection();
            });

            console.log('菜品选择器事件绑定完成');
        } catch (error) {
            console.error('绑定菜品选择器事件失败:', error);
        }
    },

    /**
     * 显示菜品选择模态框
     * @param {string} date - 日期
     * @param {string} meal - 餐次
     * @returns {boolean} 是否成功显示
     */
    showModal(date, meal) {
        try {
            if (!date || !meal) {
                console.error('显示模态框: 缺少日期或餐次信息');
                return false;
            }

            // 更新状态
            this.state.currentDate = date;
            this.state.currentMeal = meal;

            console.log(`显示菜品选择模态框: ${date} ${meal}`);

            // 清空已选菜品
            this.selectedRecipes.clear();
            $('#selectedDishes').empty();

            // 清空搜索框
            $('#recipeSearch').val('');
            this.state.lastSearchKeyword = '';

            // 清空自定义菜品输入框
            $('#customDishInput').val('');

            // 重置分类筛选
            $('#recipeCategories .nav-link').removeClass('active');
            $('#recipeCategories .nav-link[data-category="all"]').addClass('active');
            this.state.lastCategory = 'all';
            $('.recipe-card').show();

            // 获取当前输入框
            const $input = $(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);

            // 获取当前输入框中的菜品
            let currentRecipes = [];
            if (window.MenuDataManager) {
                currentRecipes = window.MenuDataManager.getRecipes(date, meal);
            }
            console.log(`从MenuDataManager获取菜品: ${currentRecipes.length}个`);

            // 如果MenuDataManager中没有数据，但输入框显示有菜品
            if (currentRecipes.length === 0 && $input.length && $input.hasClass('has-recipes')) {
                console.log('MenuDataManager中无数据，尝试从输入框恢复');
                currentRecipes = this._recoverRecipesFromInput($input, date, meal);
            }

            // 批量添加到已选菜品
            if (currentRecipes.length > 0) {
                const addedCount = this.addRecipes(currentRecipes);
                console.log(`已添加${addedCount}个菜品到选择器`);
            }

            // 更新模态框标题
            $('#modalTitle').text(`${date} ${meal} 菜品选择`);

            // 显示模态框 - 兼容Bootstrap 4和5
            const modal = $('#menuModal');
            if (modal.modal) {
                modal.modal('show');
            } else if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const bsModal = new bootstrap.Modal(modal[0]);
                bsModal.show();
            }

            // 默认选中肉类标签
            setTimeout(() => {
                $('.nav-link[data-category="肉类"]').click();
            }, 100); // 短暂延时确保DOM已完全加载

            return true;
        } catch (error) {
            console.error('显示菜品选择模态框失败:', error);
            return false;
        }
    },

    /**
     * 从输入框恢复菜品数据
     * @param {jQuery} $input - 输入框jQuery对象
     * @param {string} date - 日期
     * @param {string} meal - 餐次
     * @returns {Array} 恢复的菜品列表
     * @private
     */
    _recoverRecipesFromInput($input, date, meal) {
        let currentRecipes = [];

        // 尝试从输入框的data属性获取菜品ID
        const recipeIds = $input.data('recipe-ids');

        if (recipeIds && recipeIds.length > 0) {
            console.log(`从输入框恢复菜品数据: IDs=${recipeIds}`);

            // 创建菜品对象
            currentRecipes = recipeIds.map(id => {
                // 如果是自定义菜品(以custom_开头)
                if (id && typeof id === 'string' && id.startsWith('custom_')) {
                    return {
                        id: id,
                        name: $input.text().trim(),
                        recipe_id: null,
                        recipe_name: $input.text().trim(),
                        is_custom: true
                    };
                }

                // 普通菜品
                return {
                    id: id,
                    name: $input.text().trim(),
                    recipe_id: id,
                    recipe_name: $input.text().trim()
                };
            });

            // 更新MenuDataManager
            if (window.MenuDataManager) {
                window.MenuDataManager.setRecipes(date, meal, currentRecipes);
            }
            console.log(`已恢复菜品数据: ${currentRecipes.length}个菜品`);
        } else {
            // 如果没有保存菜品ID，尝试从文本内容创建
            const text = $input.text().trim();
            if (text && !text.includes('未选择菜品')) {
                console.log(`从文本创建菜品: 文本="${text}"`);

                // 创建自定义菜品
                const customId = 'custom_' + Date.now();
                const recipe = {
                    id: customId,
                    name: text,
                    recipe_id: null,
                    recipe_name: text,
                    is_custom: true
                };

                currentRecipes = [recipe];

                // 更新MenuDataManager
                if (window.MenuDataManager) {
                    window.MenuDataManager.setRecipes(date, meal, currentRecipes);
                }
                console.log(`已创建自定义菜品: ID=${customId}`);
            }
        }

        return currentRecipes;
    },

    /**
     * 按分类筛选菜品
     * @param {string} category - 分类名称
     * @returns {boolean} 是否筛选成功
     */
    filterByCategory(category) {
        try {
            if (category === 'all') {
                $('.recipe-card-item').show();
            } else {
                $('.recipe-card-item').each(function() {
                    const $card = $(this).find('.recipe-card');
                    if ($card.data('category') === category) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            // 如果同时有搜索关键词，需要结合搜索结果
            if (this.state.lastSearchKeyword) {
                this.searchRecipes(this.state.lastSearchKeyword);
            }

            return true;
        } catch (error) {
            console.error('筛选菜品失败:', error);
            return false;
        }
    },

    /**
     * 搜索菜品
     * @param {string} keyword - 搜索关键词
     * @returns {boolean} 是否搜索成功
     */
    searchRecipes(keyword) {
        try {
            if (!keyword) {
                // 如果没有关键词，恢复到分类筛选状态
                this.filterByCategory(this.state.lastCategory);
                return true;
            }

            // 先按分类筛选
            if (this.state.lastCategory !== 'all') {
                $('.recipe-card').hide();
                $(`.recipe-card[data-category="${this.state.lastCategory}"]`).show();
            } else {
                $('.recipe-card').show();
            }

            // 再按关键词筛选
            $('.recipe-card:visible').each(function() {
                const name = $(this).find('.card-title').text().toLowerCase();
                $(this).toggle(name.includes(keyword.toLowerCase()));
            });

            return true;
        } catch (error) {
            console.error('搜索菜品失败:', error);
            return false;
        }
    },

    /**
     * 添加自定义菜品
     * @returns {boolean} 是否添加成功
     */
    addCustomRecipe() {
        try {
            const input = $('#customDishInput');
            const name = input.val().trim();

            if (!name) {
                console.warn('添加自定义菜品: 名称为空');
                return false;
            }

            // 创建自定义菜品ID
            const customId = 'custom_' + Date.now();

            // 创建菜品对象
            const recipe = {
                id: customId,
                name: name,
                recipe_id: null,
                recipe_name: name,
                is_custom: true
            };

            // 添加到已选菜品
            if (this.addToSelection(recipe)) {
                // 清空输入框
                input.val('');
            }

            return true;
        } catch (error) {
            console.error('添加自定义菜品失败:', error);
            return false;
        }
    },

    /**
     * 批量添加菜品
     * @param {Array} recipes - 菜品列表
     * @returns {number} 成功添加的菜品数量
     */
    addRecipes(recipes) {
        if (!Array.isArray(recipes)) {
            console.error('批量添加菜品: recipes不是数组');
            return 0;
        }

        let addedCount = 0;
        recipes.forEach(recipe => {
            if (this.addToSelection(recipe, false)) {
                addedCount++;
            }
        });

        return addedCount;
    },

    /**
     * 添加到已选菜品
     * @param {Object} recipe - 菜品对象
     * @param {boolean} animate - 是否显示动画
     * @returns {boolean} 是否添加成功
     */
    addToSelection(recipe, animate = true) {
        try {
            console.log('尝试添加菜品:', recipe);

            // 验证菜品数据
            if (!recipe || !recipe.id) {
                console.warn('添加菜品: 无效的菜品数据');
                return false;
            }

            // 检查是否已经选择了该菜品
            if (this.selectedRecipes.has(recipe.id)) {
                console.log('菜品已经被选择:', recipe.name);
                this.highlightExistingRecipe(recipe.id);
                return false;
            }

            // 添加到选择集合
            this.selectedRecipes.set(recipe.id, recipe);
            console.log('菜品已添加到选择集合，当前已选菜品数量:', this.selectedRecipes.size);

            // 检查已选菜品容器是否存在
            const $selectedDishes = $('#selectedDishes');
            if (!$selectedDishes.length) {
                console.error('未找到已选菜品容器 #selectedDishes');
                return false;
            }

            // 更新UI
            const dishTag = $(`<div class="selected-recipe-tag" data-id="${recipe.id}">
                ${recipe.name}
                <span class="remove-btn">&times;</span>
            </div>`);

            // 绑定移除事件
            dishTag.find('.remove-btn').on('click', () => {
                this.removeFromSelection(recipe.id);
            });

            // 添加到已选区域
            $selectedDishes.append(dishTag);

            // 添加动画效果
            if (animate) {
                dishTag.hide().fadeIn(300);
            }

            return true;
        } catch (error) {
            console.error('添加菜品失败:', error);
            return false;
        }
    },

    /**
     * 从选择中移除菜品
     * @param {string} recipeId - 菜品ID
     * @returns {boolean} 是否移除成功
     */
    removeFromSelection(recipeId) {
        try {
            // 从选择集合中移除
            this.selectedRecipes.delete(recipeId);

            // 从DOM中移除
            $(`.selected-recipe-tag[data-id="${recipeId}"]`).fadeOut(200, function() {
                $(this).remove();
            });

            // 更新卡片选中状态
            $(`.recipe-card .card[data-id="${recipeId}"]`).removeClass('selected');

            return true;
        } catch (error) {
            console.error('移除菜品失败:', error);
            return false;
        }
    },

    /**
     * 高亮已存在的菜品
     * @param {string} recipeId - 菜品ID
     */
    highlightExistingRecipe(recipeId) {
        const tag = $(`.selected-recipe-tag[data-id="${recipeId}"]`);
        tag.addClass('highlight');
        setTimeout(() => {
            tag.removeClass('highlight');
        }, 1000);
    },

    /**
     * 保存选择
     * @returns {boolean} 是否保存成功
     */
    saveSelection() {
        try {
            const date = this.state.currentDate;
            const meal = this.state.currentMeal;

            if (!date || !meal) {
                console.error('保存选择: 缺少日期或餐次信息');
                if (window.UIManager) {
                    window.UIManager.showError('保存失败: 缺少日期或餐次信息');
                }
                return false;
            }

            // 获取所有已选菜品
            const recipes = Array.from(this.selectedRecipes.values());
            console.log(`保存选择: ${date} ${meal}, ${recipes.length}个菜品`);

            // 确保每个菜品都有recipe_id
            recipes.forEach(recipe => {
                // 如果recipe_id为空但id存在，则使用id作为recipe_id
                if (!recipe.recipe_id && recipe.id) {
                    recipe.recipe_id = recipe.id;
                    console.log(`修正菜品数据: 使用id(${recipe.id})作为recipe_id`);
                }

                // 如果是自定义菜品且没有recipe_id，设置为null
                if (recipe.is_custom && !recipe.recipe_id) {
                    recipe.recipe_id = null;
                    console.log(`自定义菜品: ${recipe.name}, recipe_id设置为null`);
                }

                // 确保recipe_name存在
                if (!recipe.recipe_name && recipe.name) {
                    recipe.recipe_name = recipe.name;
                }
            });

            // 更新菜单数据
            let updated = false;
            if (window.MenuDataManager) {
                updated = window.MenuDataManager.setRecipes(date, meal, recipes);
            }

            if (!updated) {
                console.error('保存选择: 更新菜单数据失败');
                if (window.UIManager) {
                    window.UIManager.showError('保存失败: 更新菜单数据失败');
                }
                return false;
            }

            // 更新输入框显示
            const input = $(`.menu-input[data-date="${date}"][data-meal="${meal}"]`);
            if (input.length && window.UIManager) {
                window.UIManager.updateInputDisplay(input, recipes);

                // 将菜品ID存储到输入框的data属性中
                const recipeIds = recipes.map(r => r.recipe_id || r.id || null);
                input.data('recipe-ids', recipeIds);
                console.log(`已将菜品ID保存到输入框data属性: ${recipeIds}`);
            } else {
                console.warn(`保存选择: 未找到输入框 ${date} ${meal}`);
            }

            // 关闭模态框 - 兼容Bootstrap 4和5
            const modal = $('#menuModal');
            if (modal.modal) {
                modal.modal('hide');
            } else if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const bsModal = bootstrap.Modal.getInstance(modal[0]);
                if (bsModal) {
                    bsModal.hide();
                }
            }

            // 显示保存提示
            if (window.UIManager) {
                window.UIManager.showMessage('菜品已选择，请点击"保存菜单"按钮保存到数据库', 'info');
            }

            return true;
        } catch (error) {
            console.error('保存菜品选择失败:', error);
            if (window.UIManager) {
                window.UIManager.showError('保存失败: ' + error.message);
            }
            return false;
        }
    },

    /**
     * 清空选择
     * @returns {boolean} 是否清空成功
     */
    clearSelection() {
        try {
            this.selectedRecipes.clear();
            $('#selectedDishes').empty();
            return true;
        } catch (error) {
            console.error('清空选择失败:', error);
            return false;
        }
    }
};

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RecipeSelector;
} else if (typeof window !== 'undefined') {
    window.RecipeSelector = RecipeSelector;
}
