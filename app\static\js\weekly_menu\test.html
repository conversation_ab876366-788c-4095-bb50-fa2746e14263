<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周菜单模块化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>周菜单模块化系统测试</h1>
    
    <div class="test-section">
        <h2>模块加载测试</h2>
        <button onclick="testModuleLoading()">测试模块加载</button>
        <div id="moduleLoadingResult"></div>
    </div>

    <div class="test-section">
        <h2>应用初始化测试</h2>
        <button onclick="testAppInitialization()">测试应用初始化</button>
        <div id="appInitResult"></div>
    </div>

    <div class="test-section">
        <h2>数据管理测试</h2>
        <button onclick="testDataManager()">测试数据管理</button>
        <div id="dataManagerResult"></div>
    </div>

    <div class="test-section">
        <h2>UI管理测试</h2>
        <button onclick="testUIManager()">测试UI管理</button>
        <div id="uiManagerResult"></div>
    </div>

    <div class="test-section">
        <h2>工具函数测试</h2>
        <button onclick="testCommonUtils()">测试工具函数</button>
        <div id="commonUtilsResult"></div>
    </div>

    <div class="test-section">
        <h2>控制台日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <!-- 引入模块 -->
    <script src="utils/common.js"></script>
    <script src="core/MenuDataManager.js"></script>
    <script src="core/UIManager.js"></script>
    <script src="components/VirtualScroll.js"></script>
    <script src="components/RecipeSelector.js"></script>
    <script src="core/WeeklyMenuCore.js"></script>
    <script src="weekly_menu_main.js"></script>

    <script>
        // 重写console.log以显示在页面上
        const originalLog = console.log;
        const logElement = document.getElementById('log');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logElement.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        };

        function clearLog() {
            logElement.innerHTML = '';
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${success ? 'success' : 'error'}">${message}</div>`;
        }

        function testModuleLoading() {
            console.log('开始测试模块加载...');
            
            const modules = [
                'CommonUtils',
                'MenuDataManager', 
                'UIManager',
                'VirtualScroll',
                'RecipeSelector',
                'WeeklyMenuCore',
                'WeeklyMenuApp'
            ];

            const results = modules.map(moduleName => {
                const exists = window[moduleName] !== undefined;
                console.log(`模块 ${moduleName}: ${exists ? '✓ 已加载' : '✗ 未找到'}`);
                return { name: moduleName, loaded: exists };
            });

            const allLoaded = results.every(r => r.loaded);
            const message = allLoaded ? 
                '所有模块加载成功' : 
                `部分模块加载失败: ${results.filter(r => !r.loaded).map(r => r.name).join(', ')}`;
            
            showResult('moduleLoadingResult', allLoaded, message);
        }

        function testAppInitialization() {
            console.log('开始测试应用初始化...');
            
            if (!window.WeeklyMenuApp) {
                showResult('appInitResult', false, '应用对象不存在');
                return;
            }

            try {
                const testData = {
                    '2024-01-01': {
                        '早餐': [{ id: 1, name: '测试菜品', recipe_id: 1, recipe_name: '测试菜品' }]
                    }
                };

                WeeklyMenuApp.init(testData).then(() => {
                    const status = WeeklyMenuApp.getStatus();
                    console.log('应用状态:', status);
                    showResult('appInitResult', status.initialized, '应用初始化成功');
                }).catch(error => {
                    console.error('应用初始化失败:', error);
                    showResult('appInitResult', false, `应用初始化失败: ${error.message}`);
                });
            } catch (error) {
                console.error('应用初始化异常:', error);
                showResult('appInitResult', false, `应用初始化异常: ${error.message}`);
            }
        }

        function testDataManager() {
            console.log('开始测试数据管理...');
            
            if (!window.MenuDataManager) {
                showResult('dataManagerResult', false, 'MenuDataManager 不存在');
                return;
            }

            try {
                // 测试数据操作
                const testRecipes = [
                    { id: 1, name: '测试菜品1', recipe_id: 1, recipe_name: '测试菜品1' },
                    { id: 2, name: '测试菜品2', recipe_id: 2, recipe_name: '测试菜品2' }
                ];

                MenuDataManager.setRecipes('2024-01-01', '早餐', testRecipes);
                const retrievedRecipes = MenuDataManager.getRecipes('2024-01-01', '早餐');
                
                const success = retrievedRecipes.length === 2 && retrievedRecipes[0].name === '测试菜品1';
                console.log('数据管理测试结果:', { success, retrievedRecipes });
                
                showResult('dataManagerResult', success, success ? '数据管理功能正常' : '数据管理功能异常');
            } catch (error) {
                console.error('数据管理测试失败:', error);
                showResult('dataManagerResult', false, `数据管理测试失败: ${error.message}`);
            }
        }

        function testUIManager() {
            console.log('开始测试UI管理...');
            
            if (!window.UIManager) {
                showResult('uiManagerResult', false, 'UIManager 不存在');
                return;
            }

            try {
                // 测试消息显示
                UIManager.showMessage('这是一条测试消息', 'info');
                console.log('UI管理器消息测试完成');
                
                // 测试工具函数
                const testInput = document.createElement('div');
                const testRecipes = [{ name: '测试菜品' }];
                UIManager.updateInputDisplay($(testInput), testRecipes);
                
                const success = testInput.textContent.includes('测试菜品');
                console.log('UI管理器功能测试结果:', success);
                
                showResult('uiManagerResult', success, success ? 'UI管理功能正常' : 'UI管理功能异常');
            } catch (error) {
                console.error('UI管理测试失败:', error);
                showResult('uiManagerResult', false, `UI管理测试失败: ${error.message}`);
            }
        }

        function testCommonUtils() {
            console.log('开始测试工具函数...');
            
            if (!window.CommonUtils) {
                showResult('commonUtilsResult', false, 'CommonUtils 不存在');
                return;
            }

            try {
                // 测试各种工具函数
                const tests = [
                    {
                        name: '防抖函数',
                        test: () => typeof CommonUtils.debounce === 'function'
                    },
                    {
                        name: '深拷贝',
                        test: () => {
                            const obj = { a: { b: 1 } };
                            const cloned = CommonUtils.deepClone(obj);
                            return cloned.a.b === 1 && cloned !== obj;
                        }
                    },
                    {
                        name: '菜品名称清理',
                        test: () => {
                            const cleaned = CommonUtils.cleanRecipeName('红烧肉（测试学校）');
                            return cleaned === '红烧肉';
                        }
                    },
                    {
                        name: '日期格式化',
                        test: () => {
                            const formatted = CommonUtils.formatDate(new Date('2024-01-01'), 'YYYY-MM-DD');
                            return formatted === '2024-01-01';
                        }
                    }
                ];

                const results = tests.map(test => {
                    try {
                        const result = test.test();
                        console.log(`${test.name}: ${result ? '✓' : '✗'}`);
                        return result;
                    } catch (error) {
                        console.log(`${test.name}: ✗ (${error.message})`);
                        return false;
                    }
                });

                const allPassed = results.every(r => r);
                showResult('commonUtilsResult', allPassed, 
                    allPassed ? '所有工具函数测试通过' : '部分工具函数测试失败');
            } catch (error) {
                console.error('工具函数测试失败:', error);
                showResult('commonUtilsResult', false, `工具函数测试失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testModuleLoading();
            }, 1000);
        });
    </script>
</body>
</html>
