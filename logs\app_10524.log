2025-06-16 15:03:00,060 INFO: 应用启动 - PID: 10524 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-16 16:11:59,932 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 16:12:02,116 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 16:12:04,642 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 16:12:08,999 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 16:12:30,678 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 17:29:27,458 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 17:29:33,919 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 17:29:41,663 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 17:29:45,686 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
    return render_template('weekly_menu/plan_v2.html',
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
2025-06-16 17:33:22,685 ERROR: 周菜单操作异常: 'current_week' is undefined [in D:\StudentsCMSSP\app\utils\decorators.py:121]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 110, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 353, in plan
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "D:\StudentsCMSSP\app\templates\base.html", line 308, in top-level template code
    {% block content %}{% endblock %}
  File "D:\StudentsCMSSP\app\templates\weekly_menu\plan_v2.html", line 53, in block 'content'
    value="{{ current_week.start_date }}"
  File "D:\StudentsCMSSP\app\utils\jinja_extensions.py", line 53, in _safe_getattr
    return self._original_getattr(obj, attribute, *args, **kwargs)
  File "D:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'current_week' is undefined
