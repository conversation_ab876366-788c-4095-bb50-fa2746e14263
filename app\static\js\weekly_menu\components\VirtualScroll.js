/**
 * 虚拟滚动组件
 * 用于优化大量数据的渲染性能
 * 
 * @module VirtualScroll
 * @version 2.0
 */

const VirtualScroll = {
    /**
     * 创建虚拟滚动实例
     * @param {Object} options - 配置选项
     * @param {HTMLElement} options.container - 容器元素
     * @param {Array} options.items - 数据项数组
     * @param {Function} options.itemRenderer - 项目渲染函数
     * @param {number} options.itemHeight - 每项高度
     * @param {number} options.bufferSize - 缓冲区大小
     * @returns {Object} 虚拟滚动实例
     */
    create(options) {
        return {
            // 配置
            config: {
                itemHeight: options.itemHeight || 50,
                bufferSize: options.bufferSize || 5
            },

            // 状态
            container: options.container,
            content: null,
            items: options.items || [],
            itemRenderer: options.itemRenderer || this._defaultRenderer,

            /**
             * 初始化虚拟滚动
             */
            init() {
                if (!this.container) {
                    console.error('VirtualScroll: 容器元素不存在');
                    return;
                }

                // 设置容器样式
                this.container.style.overflow = 'auto';
                this.container.style.position = 'relative';

                // 创建内容容器
                this.content = document.createElement('div');
                this.content.style.position = 'relative';
                this.container.appendChild(this.content);

                // 绑定滚动事件
                this.container.addEventListener('scroll', () => {
                    this._handleScroll();
                });

                // 初始渲染
                this.render();

                console.log('VirtualScroll 初始化完成');
            },

            /**
             * 更新数据
             * @param {Array} newItems - 新的数据项
             */
            updateItems(newItems) {
                this.items = newItems || [];
                this.render();
            },

            /**
             * 默认渲染器
             * @param {*} item - 数据项
             * @returns {string} HTML字符串
             * @private
             */
            _defaultRenderer(item) {
                return `
                    <div class="virtual-scroll-item">
                        ${JSON.stringify(item)}
                    </div>
                `;
            },

            /**
             * 处理滚动事件
             * @private
             */
            _handleScroll() {
                requestAnimationFrame(() => {
                    this.render();
                });
            },

            /**
             * 渲染可见项目
             */
            render() {
                if (!this.container || !this.content) return;

                const scrollTop = this.container.scrollTop;
                const containerHeight = this.container.clientHeight;

                // 计算可见范围
                const startIndex = Math.max(0, Math.floor(scrollTop / this.config.itemHeight) - this.config.bufferSize);
                const endIndex = Math.min(
                    this.items.length - 1,
                    Math.ceil((scrollTop + containerHeight) / this.config.itemHeight) + this.config.bufferSize
                );

                // 设置内容高度
                this.content.style.height = `${this.items.length * this.config.itemHeight}px`;

                // 生成HTML
                let html = '';

                // 渲染可见项目
                for (let i = startIndex; i <= endIndex; i++) {
                    const item = this.items[i];
                    if (item) {
                        const top = i * this.config.itemHeight;
                        html += `<div style="position:absolute;top:${top}px;width:100%;">${this.itemRenderer(item)}</div>`;
                    }
                }

                this.content.innerHTML = html;
            },

            /**
             * 销毁虚拟滚动
             */
            destroy() {
                if (this.container && this.content) {
                    this.container.removeChild(this.content);
                }
                this.container = null;
                this.content = null;
                this.items = [];
            }
        };
    }
};

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VirtualScroll;
} else if (typeof window !== 'undefined') {
    window.VirtualScroll = VirtualScroll;
}
